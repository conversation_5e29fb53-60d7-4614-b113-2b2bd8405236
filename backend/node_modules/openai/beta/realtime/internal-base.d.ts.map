{"version": 3, "file": "internal-base.d.ts", "sourceRoot": "", "sources": ["../../src/beta/realtime/internal-base.ts"], "names": [], "mappings": "OAAO,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,UAAU,EAAE;OACxD,EAAE,YAAY,EAAE;OAChB,EAAE,WAAW,EAAE;OACf,MAAM,EAAE,EAAE,WAAW,EAAE;AAE9B,qBAAa,mBAAoB,SAAQ,WAAW;IAClD;;OAEG;IACH,KAAK,CAAC,EAAE,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC;IAErC;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;gBAElB,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,GAAG,IAAI;CAMtD;AAED,KAAK,QAAQ,CAAC,CAAC,IAAI;KAAG,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;CAAE,GAAG,EAAE,CAAC;AAE7D,KAAK,cAAc,GAAG,QAAQ,CAC5B;IACE,KAAK,EAAE,CAAC,KAAK,EAAE,mBAAmB,KAAK,IAAI,CAAC;IAC5C,KAAK,EAAE,CAAC,KAAK,EAAE,mBAAmB,KAAK,IAAI,CAAC;CAC7C,GAAG;KACD,SAAS,IAAI,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,CAC5D,KAAK,EAAE,OAAO,CAAC,mBAAmB,EAAE;QAAE,IAAI,EAAE,SAAS,CAAA;KAAE,CAAC,KACrD,OAAO;CACb,CACF,CAAC;AAEF,8BAAsB,qBAAsB,SAAQ,YAAY,CAAC,cAAc,CAAC;IAC9E;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,mBAAmB,GAAG,IAAI;IAE/C;;OAEG;IACH,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;IAE9D,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IAClE,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;CAyB1E;AAED,wBAAgB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG,SAAS,CAAC,GAAG,MAAM,IAAI,WAAW,CAEzF;AAED,wBAAgB,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG,SAAS,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,CAY/F"}