-- Migration: Add AI medicine extraction fields to prescriptions table
-- Date: 2025-01-01
-- Description: Add columns to store AI-extracted medicine names and extraction status

-- Add columns for AI medicine extraction
ALTER TABLE prescriptions
ADD COLUMN IF NOT EXISTS ai_extracted_medicines TEXT[], -- Array of medicine names extracted by AI
ADD COLUMN IF NOT EXISTS ai_extraction_success BOOLEAN DEFAULT FALSE, -- Whether AI extraction was successful
ADD COLUMN IF NOT EXISTS filename VARCHAR(255); -- Original filename

-- Add index for better query performance on AI extracted medicines
CREATE INDEX IF NOT EXISTS idx_prescriptions_ai_extracted_medicines ON prescriptions USING GIN (ai_extracted_medicines);

-- Add index for AI extraction success status
CREATE INDEX IF NOT EXISTS idx_prescriptions_ai_extraction_success ON prescriptions (ai_extraction_success);

-- Update existing records to set default values
UPDATE prescriptions 
SET ai_extraction_success = FALSE 
WHERE ai_extraction_success IS NULL;

-- Add comment to document the new columns
COMMENT ON COLUMN prescriptions.ai_extracted_medicines IS 'Array of medicine names extracted by OpenAI from prescription text';
COMMENT ON COLUMN prescriptions.ai_extraction_success IS 'Boolean indicating if AI medicine extraction was successful';
COMMENT ON COLUMN prescriptions.extracted_text IS 'Full text extracted from prescription by AWS Textract';
COMMENT ON COLUMN prescriptions.filename IS 'Original filename of the uploaded prescription';
