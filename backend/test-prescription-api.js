const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testPrescriptionAPI() {
  console.log('🧪 Testing prescription API response...');
  
  try {
    // Get prescriptions with medicines (same query as backend service)
    const { data: prescriptions, error } = await supabase
      .from('prescriptions')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        doctor:doctors(id, users!doctors_id_fkey(name, email), specialization),
        medicines(id, name, dosage, frequency, duration, start_date, end_date, instructions, side_effects)
      `)
      .limit(5);
    
    if (error) {
      console.error('❌ Error fetching prescriptions:', error);
      return;
    }
    
    console.log(`✅ Found ${prescriptions.length} prescriptions`);
    
    prescriptions.forEach((prescription, index) => {
      console.log(`\n📋 Prescription ${index + 1}:`);
      console.log(`   ID: ${prescription.id}`);
      console.log(`   Filename: ${prescription.filename}`);
      console.log(`   Status: ${prescription.status}`);
      console.log(`   AI Extracted Medicines: ${JSON.stringify(prescription.ai_extracted_medicines)}`);
      console.log(`   AI Extraction Success: ${prescription.ai_extraction_success}`);
      console.log(`   Medicines Count: ${prescription.medicines ? prescription.medicines.length : 0}`);
      
      if (prescription.medicines && prescription.medicines.length > 0) {
        console.log('   💊 Medicines:');
        prescription.medicines.forEach((medicine, medIndex) => {
          console.log(`      ${medIndex + 1}. ${medicine.name} (${medicine.dosage}) - ${medicine.frequency}`);
        });
      } else {
        console.log('   ⚠️ No medicines found in database');
      }
      
      if (prescription.ai_extracted_medicines && prescription.ai_extracted_medicines.length > 0) {
        console.log('   🤖 AI Extracted Names:');
        prescription.ai_extracted_medicines.forEach((name, nameIndex) => {
          console.log(`      ${nameIndex + 1}. ${name}`);
        });
      }
    });
    
    // Also check if there are any medicines in the medicines table
    const { data: allMedicines, error: medicinesError } = await supabase
      .from('medicines')
      .select('*')
      .limit(10);
    
    if (medicinesError) {
      console.error('❌ Error fetching medicines:', medicinesError);
    } else {
      console.log(`\n💊 Total medicines in database: ${allMedicines.length}`);
      allMedicines.forEach((medicine, index) => {
        console.log(`   ${index + 1}. ${medicine.name} (${medicine.dosage}) - Prescription: ${medicine.prescription_id}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

testPrescriptionAPI();
