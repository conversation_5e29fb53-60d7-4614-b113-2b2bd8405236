import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

@Injectable()
export class OpenAiService {
  private readonly logger = new Logger(OpenAiService.name);
  private readonly openai: OpenAI;
  private readonly isEnabled: boolean;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('openai.apiKey');
    this.isEnabled = !!apiKey;

    if (this.isEnabled) {
      this.openai = new OpenAI({
        apiKey: apiKey,
      });
      this.logger.log('OpenAI service initialized successfully');
    } else {
      this.logger.warn('OpenAI API key not provided - service disabled');
    }
  }

  async extractMedicineNames(extractedText: string): Promise<{
    medicines: string[];
    success: boolean;
    error?: string;
  }> {
    if (!this.isEnabled) {
      this.logger.warn('OpenAI not configured - cannot extract medicine names');
      return {
        medicines: [],
        success: false,
        error: 'OpenAI not configured'
      };
    }

    try {
      const prompt = `
You are a medical AI assistant specialized in analyzing prescription documents. 
Your task is to extract ONLY the medicine names from the following prescription text.

Instructions:
1. Extract only the actual medicine/drug names (generic or brand names)
2. Do not include dosages, frequencies, or instructions
3. Do not include medical conditions or symptoms
4. Return only the medicine names, one per line
5. If no medicines are found, return "No medicines found"
6. Be very precise - only return actual medication names

Prescription text:
${extractedText}

Medicine names (one per line):`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a medical AI assistant that extracts medicine names from prescription text. Only return the medicine names, nothing else.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.1,
      });

      const content = response.choices[0]?.message?.content?.trim();
      if (!content || content === 'No medicines found') {
        this.logger.log('No medicines found in the prescription text');
        return { medicines: [], success: true };
      }

      const medicines = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.toLowerCase().includes('no medicines'))
        .map(line => {
          // Remove numbering and bullet points
          return line.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, '').trim();
        })
        .filter(medicine => medicine.length > 0);

      this.logger.log(`Successfully extracted ${medicines.length} medicine names: ${medicines.join(', ')}`);
      return {
        medicines,
        success: true
      };
    } catch (error: any) {
      this.logger.error('Failed to extract medicine names:', error);
      return {
        medicines: [],
        success: false,
        error: error.message || 'Unknown error occurred'
      };
    }
  }

  async testConnection(): Promise<{
    success: boolean;
    error?: string;
    model?: string;
  }> {
    if (!this.isEnabled) {
      return {
        success: false,
        error: 'OpenAI not configured'
      };
    }

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: 'Hello, this is a test message. Please respond with "OpenAI connection successful".'
          }
        ],
        max_tokens: 50,
      });

      const content = response.choices[0]?.message?.content;
      return {
        success: true,
        model: response.model
      };
    } catch (error: any) {
      this.logger.error('OpenAI connection test failed:', error);
      return {
        success: false,
        error: error.message || 'Connection test failed'
      };
    }
  }
}
