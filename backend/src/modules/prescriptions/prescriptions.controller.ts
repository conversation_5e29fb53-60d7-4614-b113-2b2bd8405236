import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { PrescriptionsService } from './prescriptions.service';
import { CreatePrescriptionDto, UpdatePrescriptionDto, PrescriptionQueryDto, ProcessPrescriptionDto } from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../../common/types';

@ApiTags('prescriptions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('prescriptions')
export class PrescriptionsController {
  constructor(private readonly prescriptionsService: PrescriptionsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new prescription' })
  @ApiResponse({ status: 201, description: 'Prescription created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async create(
    @Body() createPrescriptionDto: CreatePrescriptionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.create(createPrescriptionDto, currentUser);
  }

  @Post('upload')
  @ApiOperation({ summary: 'Upload prescription file and extract text using AWS Textract' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Prescription uploaded and processed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @UseInterceptors(FileInterceptor('file'))
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async uploadPrescription(
    @UploadedFile() file: Express.Multer.File,
    @Body('patient_id') patientId: string,
    @Body('patientId') patientIdAlt: string, // Support both formats
    @Body('doctor_id') doctorId: string,
    @CurrentUser() currentUser: User,
  ) {
    // Use patient_id if provided, otherwise use patientId, otherwise use current user ID for patient users
    const finalPatientId = patientId || patientIdAlt || (currentUser.role === 'patient' ? currentUser.id : undefined);

    if (!finalPatientId) {
      throw new BadRequestException('Patient ID is required');
    }

    const result = await this.prescriptionsService.uploadAndProcessPrescription(
      file,
      finalPatientId,
      doctorId,
      currentUser,
    );

    // Transform snake_case to camelCase for frontend compatibility
    return this.transformPrescriptionResponse(result);
  }

  @Get()
  @ApiOperation({ summary: 'Get all prescriptions' })
  @ApiQuery({ name: 'patient_id', required: false, description: 'Filter by patient ID' })
  @ApiQuery({ name: 'doctor_id', required: false, description: 'Filter by doctor ID' })
  @ApiQuery({ name: 'status', required: false, enum: ['processing', 'completed', 'failed', 'all'], description: 'Filter by status' })
  @ApiQuery({ name: 'filename', required: false, description: 'Filter by filename' })
  @ApiResponse({ status: 200, description: 'Prescriptions retrieved successfully' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findAll(
    @Query() query: PrescriptionQueryDto,
    @CurrentUser() currentUser: User,
  ) {
    const prescriptions = await this.prescriptionsService.findAll(query, currentUser);
    return prescriptions.map(prescription => this.transformPrescriptionResponse(prescription));
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Get prescriptions for a specific patient' })
  @ApiResponse({ status: 200, description: 'Patient prescriptions retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByPatient(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.findByPatient(patientId, currentUser);
  }

  @Get('doctor/:doctorId')
  @ApiOperation({ summary: 'Get prescriptions for a specific doctor' })
  @ApiResponse({ status: 200, description: 'Doctor prescriptions retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Doctor not found' })
  @Roles('doctor', 'hospital', 'admin', 'insurance')
  async findByDoctor(
    @Param('doctorId', ParseUUIDPipe) doctorId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.findByDoctor(doctorId, currentUser);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get prescription by ID' })
  @ApiResponse({ status: 200, description: 'Prescription retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.findOne(id, currentUser);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update prescription' })
  @ApiResponse({ status: 200, description: 'Prescription updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('doctor', 'hospital', 'admin')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePrescriptionDto: UpdatePrescriptionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.update(id, updatePrescriptionDto, currentUser);
  }

  @Patch(':id/process')
  @ApiOperation({ summary: 'Process prescription (update status and extracted text)' })
  @ApiResponse({ status: 200, description: 'Prescription processed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('doctor', 'hospital', 'admin')
  async processPrescription(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() processPrescriptionDto: ProcessPrescriptionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.processPrescription(id, processPrescriptionDto, currentUser);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete prescription' })
  @ApiResponse({ status: 200, description: 'Prescription deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Only administrators can delete prescriptions' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('admin')
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    await this.prescriptionsService.remove(id, currentUser);
    return { message: 'Prescription deleted successfully' };
  }



  /**
   * Transform prescription response from snake_case to camelCase for frontend compatibility
   */
  private transformPrescriptionResponse(prescription: any) {
    return {
      id: prescription.id,
      patientId: prescription.patient_id,
      doctorId: prescription.doctor_id,
      prescriptionDate: prescription.prescription_date,
      filename: prescription.filename,
      fileUrl: prescription.file_url,
      extractedText: prescription.extracted_text,
      status: prescription.status,
      textractConfidence: prescription.textract_confidence,
      medicineInfo: prescription.medicine_info,
      aiExtractedMedicines: prescription.ai_extracted_medicines,
      aiExtractionSuccess: prescription.ai_extraction_success,
      errorMessage: prescription.error_message,
      createdAt: prescription.created_at,
      updatedAt: prescription.updated_at,
      medicines: prescription.medicines || [],
    };
  }
}
