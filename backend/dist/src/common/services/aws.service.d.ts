import { ConfigService } from '@nestjs/config';
import { OpenAiService } from './openai.service';
export declare class AwsService {
    private readonly configService;
    private readonly openAiService;
    private readonly logger;
    private readonly textractClient;
    private readonly s3Client;
    private readonly bucketName;
    constructor(configService: ConfigService, openAiService: OpenAiService);
    uploadFileToS3(file: Buffer, fileName: string, contentType: string): Promise<string>;
    extractTextFromPrescription(fileBuffer: Buffer, mimeType?: string): Promise<{
        extractedText: string;
        confidence: number;
        medicineInfo: any[];
        aiExtractedMedicines: string[];
        aiExtractionSuccess: boolean;
    }>;
    private extractMedicineInformation;
    private getTextFromBlock;
    private categorizeField;
    private extractTableData;
}
