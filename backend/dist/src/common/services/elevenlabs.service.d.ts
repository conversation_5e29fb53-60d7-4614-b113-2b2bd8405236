import { ConfigService } from '@nestjs/config';
export interface ConversationalAgentOptions {
    agentId?: string;
    phoneNumber: string;
    patientName?: string;
    medicineName?: string;
    dosage?: string;
    patientId?: string;
    medicineId?: string;
    reminderId?: string;
}
export interface TextToSpeechOptions {
    text: string;
    voiceId?: string;
    modelId?: string;
}
export interface ConversationalCallResponse {
    success: boolean;
    callId?: string;
    error?: string;
}
export interface TextToSpeechResponse {
    success: boolean;
    audioBuffer?: Buffer;
    error?: string;
}
export declare class ElevenLabsService {
    private readonly configService;
    private readonly logger;
    private readonly elevenLabsClient;
    private readonly isEnabled;
    private readonly defaultVoiceId;
    private readonly agentId;
    private readonly agentPhoneNumberId;
    constructor(configService: ConfigService);
    makeConversationalCall(options: ConversationalAgentOptions): Promise<ConversationalCallResponse>;
    generateSpeech(options: TextToSpeechOptions): Promise<TextToSpeechResponse>;
    generateMedicationReminderSpeech(patientName: string, medicineName: string, dosage: string, voiceId?: string): Promise<TextToSpeechResponse>;
    private generateMedicationReminderMessage;
    getAvailableVoices(): Promise<any[]>;
    getServiceStatus(): {
        enabled: boolean;
        configured: boolean;
        agentConfigured: boolean;
        voiceId: string;
    };
    createAgent(name: string, prompt: string): Promise<any>;
    generateBulkSpeech(messages: Array<{
        text: string;
        voiceId?: string;
        fileName?: string;
    }>): Promise<Array<TextToSpeechResponse & {
        fileName?: string;
    }>>;
}
