"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OpenAiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAiService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const openai_1 = require("openai");
let OpenAiService = OpenAiService_1 = class OpenAiService {
    configService;
    logger = new common_1.Logger(OpenAiService_1.name);
    openai;
    isEnabled;
    constructor(configService) {
        this.configService = configService;
        const apiKey = this.configService.get('openai.apiKey');
        this.isEnabled = !!apiKey;
        if (this.isEnabled) {
            this.openai = new openai_1.default({
                apiKey: apiKey,
            });
            this.logger.log('OpenAI service initialized successfully');
        }
        else {
            this.logger.warn('OpenAI API key not provided - service disabled');
        }
    }
    async extractMedicineNames(extractedText) {
        if (!this.isEnabled) {
            this.logger.warn('OpenAI not configured - cannot extract medicine names');
            return {
                medicines: [],
                success: false,
                error: 'OpenAI not configured'
            };
        }
        try {
            const prompt = `
You are a medical AI assistant specialized in analyzing prescription documents. 
Your task is to extract ONLY the medicine names from the following prescription text.

Instructions:
1. Extract only the actual medicine/drug names (generic or brand names)
2. Do not include dosages, frequencies, or instructions
3. Do not include medical conditions or symptoms
4. Return only the medicine names, one per line
5. If no medicines are found, return "No medicines found"
6. Be very precise - only return actual medication names

Prescription text:
${extractedText}

Medicine names (one per line):`;
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a medical AI assistant that extracts medicine names from prescription text. Only return the medicine names, nothing else.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 500,
                temperature: 0.1,
            });
            const content = response.choices[0]?.message?.content?.trim();
            if (!content || content === 'No medicines found') {
                this.logger.log('No medicines found in the prescription text');
                return { medicines: [], success: true };
            }
            const medicines = content
                .split('\n')
                .map(line => line.trim())
                .filter(line => line && !line.toLowerCase().includes('no medicines'))
                .map(line => {
                return line.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, '').trim();
            })
                .filter(medicine => medicine.length > 0);
            this.logger.log(`Successfully extracted ${medicines.length} medicine names: ${medicines.join(', ')}`);
            return {
                medicines,
                success: true
            };
        }
        catch (error) {
            this.logger.error('Failed to extract medicine names:', error);
            return {
                medicines: [],
                success: false,
                error: error.message || 'Unknown error occurred'
            };
        }
    }
    async analyzeConversationForMedicineIntake(transcript, patientName, medicineName, dosage) {
        if (!this.isEnabled) {
            return {
                success: false,
                medicineConfirmed: false,
                confidence: 0,
                reasoning: 'OpenAI not configured',
                error: 'OpenAI not configured'
            };
        }
        try {
            const conversationText = transcript
                .map(turn => `${turn.role.toUpperCase()}: ${turn.message}`)
                .join('\n');
            const prompt = `You are analyzing a phone conversation between an AI agent and a patient about medication adherence.

CONVERSATION CONTEXT:
- Patient Name: ${patientName}
- Medicine: ${medicineName}
- Dosage: ${dosage}

CONVERSATION TRANSCRIPT:
${conversationText}

ANALYSIS TASK:
Determine if the patient explicitly confirmed that they have taken or will take their prescribed medication during this conversation.

RESPONSE FORMAT (JSON only):
{
  "medicineConfirmed": boolean,
  "confidence": number (0-100),
  "reasoning": "Brief explanation of your decision"
}

GUIDELINES:
- Only return true if the patient clearly confirms they took the medicine or will take it
- Phrases like "yes", "I took it", "I'll take it now", "already took it" indicate confirmation
- Phrases like "maybe", "I forgot", "I don't know", "later" do NOT indicate confirmation
- If the conversation is unclear or the patient doesn't respond, return false
- Confidence should be 90-100 for clear confirmations, 70-89 for likely confirmations, below 70 for uncertain cases`;
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a medical AI assistant that analyzes patient conversations for medication adherence confirmation. Always respond with valid JSON only.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 300,
                temperature: 0.1,
            });
            const content = response.choices[0]?.message?.content?.trim();
            if (!content) {
                throw new Error('No response from OpenAI');
            }
            let analysis;
            try {
                analysis = JSON.parse(content);
            }
            catch (parseError) {
                this.logger.error('Failed to parse OpenAI response as JSON:', content);
                throw new Error('Invalid JSON response from OpenAI');
            }
            if (typeof analysis.medicineConfirmed !== 'boolean' ||
                typeof analysis.confidence !== 'number' ||
                typeof analysis.reasoning !== 'string') {
                throw new Error('Invalid response structure from OpenAI');
            }
            this.logger.log(`Conversation analysis completed: ${analysis.medicineConfirmed ? 'CONFIRMED' : 'NOT CONFIRMED'} (${analysis.confidence}% confidence)`);
            return {
                success: true,
                medicineConfirmed: analysis.medicineConfirmed,
                confidence: analysis.confidence,
                reasoning: analysis.reasoning
            };
        }
        catch (error) {
            this.logger.error('Failed to analyze conversation:', error);
            return {
                success: false,
                medicineConfirmed: false,
                confidence: 0,
                reasoning: 'Analysis failed',
                error: error.message || 'Conversation analysis failed'
            };
        }
    }
    async testConnection() {
        if (!this.isEnabled) {
            return {
                success: false,
                error: 'OpenAI not configured'
            };
        }
        try {
            const response = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'user',
                        content: 'Hello, this is a test message. Please respond with "OpenAI connection successful".'
                    }
                ],
                max_tokens: 50,
            });
            const content = response.choices[0]?.message?.content;
            return {
                success: true,
                model: response.model
            };
        }
        catch (error) {
            this.logger.error('OpenAI connection test failed:', error);
            return {
                success: false,
                error: error.message || 'Connection test failed'
            };
        }
    }
};
exports.OpenAiService = OpenAiService;
exports.OpenAiService = OpenAiService = OpenAiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], OpenAiService);
//# sourceMappingURL=openai.service.js.map