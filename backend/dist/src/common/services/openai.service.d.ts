import { ConfigService } from '@nestjs/config';
export declare class OpenAiService {
    private readonly configService;
    private readonly logger;
    private readonly openai;
    private readonly isEnabled;
    constructor(configService: ConfigService);
    extractMedicineNames(extractedText: string): Promise<{
        medicines: string[];
        success: boolean;
        error?: string;
    }>;
    testConnection(): Promise<{
        success: boolean;
        error?: string;
        model?: string;
    }>;
}
