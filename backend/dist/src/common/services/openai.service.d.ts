import { ConfigService } from '@nestjs/config';
export declare class OpenAiService {
    private readonly configService;
    private readonly logger;
    private readonly openai;
    private readonly isEnabled;
    constructor(configService: ConfigService);
    extractMedicineNames(extractedText: string): Promise<{
        medicines: string[];
        success: boolean;
        error?: string;
    }>;
    analyzeConversationForMedicineIntake(transcript: Array<{
        role: string;
        message: string;
    }>, patientName: string, medicineName: string, dosage: string): Promise<{
        success: boolean;
        medicineConfirmed: boolean;
        confidence: number;
        reasoning: string;
        error?: string;
    }>;
    testConnection(): Promise<{
        success: boolean;
        error?: string;
        model?: string;
    }>;
}
