import { SupabaseService } from '../../config/supabase.service';
import { User, Medicine } from '../../common/types';
import { CreateMedicineDto, UpdateMedicineDto, MedicineQueryDto } from './dto';
export declare class MedicinesService {
    private readonly supabaseService;
    constructor(supabaseService: SupabaseService);
    create(createMedicineDto: CreateMedicineDto, currentUser: User): Promise<Medicine>;
    findAll(query: MedicineQueryDto, currentUser: User): Promise<Medicine[]>;
    findOne(id: string, currentUser: User): Promise<Medicine>;
    update(id: string, updateMedicineDto: UpdateMedicineDto, currentUser: User): Promise<Medicine>;
    remove(id: string, currentUser: User): Promise<void>;
    findByPatient(patientId: string, currentUser: User): Promise<Medicine[]>;
    findByPrescription(prescriptionId: string, currentUser: User): Promise<Medicine[]>;
    createMedicinesFromExtractedNames(extractedMedicineNames: string[], prescriptionId: string, patientId: string, currentUser: User): Promise<Medicine[]>;
    private parseMedicineName;
}
