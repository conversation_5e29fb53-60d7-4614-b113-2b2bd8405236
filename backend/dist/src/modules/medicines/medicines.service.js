"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicinesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
let MedicinesService = class MedicinesService {
    supabaseService;
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async create(createMedicineDto, currentUser) {
        const supabase = this.supabaseService.getAdminClient();
        const { data: prescription, error: prescriptionError } = await supabase
            .from('prescriptions')
            .select('id, patient_id, doctor_id')
            .eq('id', createMedicineDto.prescription_id)
            .single();
        if (prescriptionError || !prescription) {
            throw new common_1.NotFoundException('Prescription not found');
        }
        if (currentUser.role === 'patient' && prescription.patient_id !== currentUser.id) {
            throw new common_1.ForbiddenException('You can only add medicines to your own prescriptions');
        }
        if (currentUser.role === 'doctor' && prescription.doctor_id !== currentUser.id) {
            throw new common_1.ForbiddenException('You can only add medicines to your own prescriptions');
        }
        const { data: patient, error: patientError } = await supabase
            .from('patients')
            .select('id')
            .eq('id', createMedicineDto.patient_id)
            .single();
        if (patientError || !patient) {
            throw new common_1.NotFoundException('Patient not found');
        }
        if (prescription.patient_id !== createMedicineDto.patient_id) {
            throw new common_1.BadRequestException('Patient ID does not match prescription');
        }
        const startDate = new Date(createMedicineDto.start_date);
        const endDate = new Date(createMedicineDto.end_date);
        if (endDate <= startDate) {
            throw new common_1.BadRequestException('End date must be after start date');
        }
        const { data, error } = await supabase
            .from('medicines')
            .insert({
            name: createMedicineDto.name,
            dosage: createMedicineDto.dosage,
            frequency: createMedicineDto.frequency,
            duration: createMedicineDto.duration,
            instructions: createMedicineDto.instructions,
            side_effects: createMedicineDto.side_effects,
            start_date: createMedicineDto.start_date,
            end_date: createMedicineDto.end_date,
            prescription_id: createMedicineDto.prescription_id,
            patient_id: createMedicineDto.patient_id,
        })
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to create medicine: ${error.message}`);
        }
        return data;
    }
    async findAll(query, currentUser) {
        const supabase = this.supabaseService.getClient();
        let dbQuery = supabase
            .from('medicines')
            .select(`
        *,
        prescription:prescriptions(id, file_url, status),
        patient:patients(id, users!patients_id_fkey(name, email))
      `);
        if (currentUser.role === 'patient') {
            dbQuery = dbQuery.eq('patient_id', currentUser.id);
        }
        else if (currentUser.role === 'doctor') {
            const { data: patientIds } = await supabase
                .from('patients')
                .select('id')
                .eq('assigned_doctor_id', currentUser.id);
            const ids = patientIds?.map(p => p.id) || [];
            if (ids.length > 0) {
                dbQuery = dbQuery.in('patient_id', ids);
            }
            else {
                return [];
            }
        }
        if (query.patient_id) {
            if (currentUser.role === 'patient' && query.patient_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view your own medicines');
            }
            dbQuery = dbQuery.eq('patient_id', query.patient_id);
        }
        if (query.prescription_id) {
            dbQuery = dbQuery.eq('prescription_id', query.prescription_id);
        }
        if (query.name) {
            dbQuery = dbQuery.ilike('name', `%${query.name}%`);
        }
        const today = new Date().toISOString().split('T')[0];
        if (query.status === 'active') {
            dbQuery = dbQuery.lte('start_date', today).gte('end_date', today);
        }
        else if (query.status === 'completed') {
            dbQuery = dbQuery.lt('end_date', today);
        }
        const { data, error } = await dbQuery.order('created_at', { ascending: false });
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch medicines: ${error.message}`);
        }
        return data || [];
    }
    async findOne(id, currentUser) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('medicines')
            .select(`
        *,
        prescription:prescriptions(id, file_url, status, doctor_id),
        patient:patients(id, users!patients_id_fkey(name, email), assigned_doctor_id)
      `)
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Medicine with ID ${id} not found`);
        }
        if (currentUser.role === 'patient' && data.patient_id !== currentUser.id) {
            throw new common_1.ForbiddenException('You can only view your own medicines');
        }
        else if (currentUser.role === 'doctor') {
            const hasAccess = data.prescription?.doctor_id === currentUser.id ||
                data.patient?.assigned_doctor_id === currentUser.id;
            if (!hasAccess) {
                throw new common_1.ForbiddenException('You can only view medicines for your patients');
            }
        }
        return data;
    }
    async update(id, updateMedicineDto, currentUser) {
        const supabase = this.supabaseService.getAdminClient();
        const existingMedicine = await this.findOne(id, currentUser);
        if (updateMedicineDto.start_date || updateMedicineDto.end_date) {
            const startDate = new Date(updateMedicineDto.start_date || existingMedicine.start_date);
            const endDate = new Date(updateMedicineDto.end_date || existingMedicine.end_date);
            if (endDate <= startDate) {
                throw new common_1.BadRequestException('End date must be after start date');
            }
        }
        const { data, error } = await supabase
            .from('medicines')
            .update({
            ...(updateMedicineDto.name && { name: updateMedicineDto.name }),
            ...(updateMedicineDto.dosage && { dosage: updateMedicineDto.dosage }),
            ...(updateMedicineDto.frequency && { frequency: updateMedicineDto.frequency }),
            ...(updateMedicineDto.duration && { duration: updateMedicineDto.duration }),
            ...(updateMedicineDto.instructions && { instructions: updateMedicineDto.instructions }),
            ...(updateMedicineDto.side_effects !== undefined && { side_effects: updateMedicineDto.side_effects }),
            ...(updateMedicineDto.start_date && { start_date: updateMedicineDto.start_date }),
            ...(updateMedicineDto.end_date && { end_date: updateMedicineDto.end_date }),
        })
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to update medicine: ${error.message}`);
        }
        return data;
    }
    async remove(id, currentUser) {
        if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only doctors, hospitals, and admins can delete medicines');
        }
        const supabase = this.supabaseService.getAdminClient();
        await this.findOne(id, currentUser);
        const { error } = await supabase
            .from('medicines')
            .delete()
            .eq('id', id);
        if (error) {
            throw new common_1.BadRequestException(`Failed to delete medicine: ${error.message}`);
        }
    }
    async findByPatient(patientId, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== patientId) {
            throw new common_1.ForbiddenException('You can only view your own medicines');
        }
        return this.findAll({ patient_id: patientId }, currentUser);
    }
    async findByPrescription(prescriptionId, currentUser) {
        return this.findAll({ prescription_id: prescriptionId }, currentUser);
    }
    async createMedicinesFromExtractedNames(extractedMedicineNames, prescriptionId, patientId, currentUser) {
        if (!extractedMedicineNames || extractedMedicineNames.length === 0) {
            return [];
        }
        const supabase = this.supabaseService.getAdminClient();
        const createdMedicines = [];
        const startDate = new Date().toISOString().split('T')[0];
        const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        for (const medicineName of extractedMedicineNames) {
            try {
                const parsedMedicine = this.parseMedicineName(medicineName);
                const { data, error } = await supabase
                    .from('medicines')
                    .insert({
                    name: parsedMedicine.name,
                    dosage: parsedMedicine.dosage,
                    frequency: 'As prescribed',
                    duration: '30',
                    instructions: 'Take as directed by your doctor',
                    start_date: startDate,
                    end_date: endDate,
                    prescription_id: prescriptionId,
                    patient_id: patientId,
                    is_active: true,
                })
                    .select()
                    .single();
                if (error) {
                    console.error(`Failed to create medicine record for "${medicineName}":`, error);
                    continue;
                }
                createdMedicines.push(data);
            }
            catch (error) {
                console.error(`Error processing medicine "${medicineName}":`, error);
                continue;
            }
        }
        return createdMedicines;
    }
    parseMedicineName(fullName) {
        const cleanName = fullName.trim();
        let name = cleanName
            .replace(/^(TAB\.|TABLET|CAP\.|CAPSULE|SYR\.|SYRUP)\s*/i, '')
            .trim();
        const dosageMatch = name.match(/(\d+(?:\.\d+)?\s*(?:MG|MCG|G|ML|IU|%|UNITS?))\s*(TABLET|TAB|CAPSULE|CAP|ML)?$/i);
        let dosage = 'As prescribed';
        if (dosageMatch) {
            dosage = dosageMatch[1].toUpperCase();
            name = name.replace(dosageMatch[0], '').trim();
        }
        name = name.replace(/\s+(TABLET|TAB|CAPSULE|CAP|SYRUP|SYR)$/i, '').trim();
        return {
            name: name || cleanName,
            dosage,
        };
    }
};
exports.MedicinesService = MedicinesService;
exports.MedicinesService = MedicinesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], MedicinesService);
//# sourceMappingURL=medicines.service.js.map