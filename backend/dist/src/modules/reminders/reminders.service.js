"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RemindersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemindersService = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const supabase_service_1 = require("../../config/supabase.service");
const twilio_service_1 = require("../../common/services/twilio.service");
const elevenlabs_service_1 = require("../../common/services/elevenlabs.service");
const schedule_1 = require("@nestjs/schedule");
let RemindersService = RemindersService_1 = class RemindersService {
    supabaseService;
    twilioService;
    elevenLabsService;
    eventEmitter;
    logger = new common_1.Logger(RemindersService_1.name);
    constructor(supabaseService, twilioService, elevenLabsService, eventEmitter) {
        this.supabaseService = supabaseService;
        this.twilioService = twilioService;
        this.elevenLabsService = elevenLabsService;
        this.eventEmitter = eventEmitter;
    }
    async create(createReminderDto, currentUser) {
        if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only patients, doctors, hospitals, and admins can create reminders');
        }
        const supabase = this.supabaseService.getAdminClient();
        const { data: patient, error: patientError } = await supabase
            .from('patients')
            .select('id, assigned_doctor_id')
            .eq('id', createReminderDto.patient_id)
            .single();
        if (patientError || !patient) {
            throw new common_1.NotFoundException('Patient not found');
        }
        if (currentUser.role === 'patient' && currentUser.id !== createReminderDto.patient_id) {
            throw new common_1.ForbiddenException('You can only create reminders for yourself');
        }
        else if (currentUser.role === 'doctor' && patient.assigned_doctor_id !== currentUser.id) {
            throw new common_1.ForbiddenException('You can only create reminders for your assigned patients');
        }
        const { data: medicine, error: medicineError } = await supabase
            .from('medicines')
            .select('id, patient_id')
            .eq('id', createReminderDto.medicine_id)
            .single();
        if (medicineError || !medicine) {
            throw new common_1.NotFoundException('Medicine not found');
        }
        if (medicine.patient_id !== createReminderDto.patient_id) {
            throw new common_1.BadRequestException('Medicine does not belong to the specified patient');
        }
        const scheduledTime = new Date(createReminderDto.scheduled_time);
        if (scheduledTime <= new Date()) {
            throw new common_1.BadRequestException('Scheduled time must be in the future');
        }
        const { data, error } = await supabase
            .from('reminders')
            .insert({
            patient_id: createReminderDto.patient_id,
            medicine_id: createReminderDto.medicine_id,
            reminder_type: createReminderDto.reminder_type,
            scheduled_time: createReminderDto.scheduled_time,
            message: createReminderDto.message,
            is_recurring: createReminderDto.is_recurring || false,
            recurrence_pattern: createReminderDto.recurrence_pattern,
            recurrence_interval: createReminderDto.recurrence_interval || 1,
            recurrence_days: createReminderDto.recurrence_days,
            end_date: createReminderDto.end_date,
            status: 'pending',
            is_active: true,
        })
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to create reminder: ${error.message}`);
        }
        return data;
    }
    async findAll(query, currentUser) {
        const supabase = this.supabaseService.getAdminClient();
        let dbQuery = supabase
            .from('reminders')
            .select('*');
        if (currentUser.role === 'patient') {
            dbQuery = dbQuery.eq('patient_id', currentUser.id);
        }
        else if (currentUser.role === 'doctor') {
            const { data: patientIds } = await supabase
                .from('patients')
                .select('id')
                .eq('assigned_doctor_id', currentUser.id);
            const ids = patientIds?.map(p => p.id) || [];
            if (ids.length > 0) {
                dbQuery = dbQuery.in('patient_id', ids);
            }
            else {
                return [];
            }
        }
        if (query.patient_id) {
            if (currentUser.role === 'patient' && query.patient_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view your own reminders');
            }
            dbQuery = dbQuery.eq('patient_id', query.patient_id);
        }
        if (query.medicine_id) {
            dbQuery = dbQuery.eq('medicine_id', query.medicine_id);
        }
        if (query.status && query.status !== 'all') {
            dbQuery = dbQuery.eq('status', query.status);
        }
        if (query.reminder_type && query.reminder_type !== 'all') {
            dbQuery = dbQuery.eq('reminder_type', query.reminder_type);
        }
        if (query.date_from) {
            dbQuery = dbQuery.gte('scheduled_time', query.date_from);
        }
        if (query.date_to) {
            dbQuery = dbQuery.lte('scheduled_time', query.date_to);
        }
        if (query.is_active !== undefined) {
            dbQuery = dbQuery.eq('is_active', query.is_active);
        }
        const { data, error } = await dbQuery.order('scheduled_time', { ascending: true });
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch reminders: ${error.message}`);
        }
        return data || [];
    }
    async getTodaysReminders(currentUser) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        return this.findAll({
            date_from: today.toISOString(),
            date_to: tomorrow.toISOString(),
            is_active: true,
        }, currentUser);
    }
    async getUpcomingReminders(currentUser, hours = 24) {
        const now = new Date();
        const futureTime = new Date(now.getTime() + hours * 60 * 60 * 1000);
        return this.findAll({
            date_from: now.toISOString(),
            date_to: futureTime.toISOString(),
            status: 'pending',
            is_active: true,
        }, currentUser);
    }
    async getOverdueReminders(currentUser) {
        const now = new Date();
        return this.findAll({
            date_to: now.toISOString(),
            status: 'pending',
            is_active: true,
        }, currentUser);
    }
    async findOne(id, currentUser) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('reminders')
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email), assigned_doctor_id),
        medicine:medicines(id, name, dosage, frequency, instructions)
      `)
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Reminder with ID ${id} not found`);
        }
        if (currentUser.role === 'patient' && data.patient_id !== currentUser.id) {
            throw new common_1.ForbiddenException('You can only view your own reminders');
        }
        else if (currentUser.role === 'doctor') {
            const hasAccess = data.patient?.assigned_doctor_id === currentUser.id;
            if (!hasAccess) {
                throw new common_1.ForbiddenException('You can only view reminders for your patients');
            }
        }
        return data;
    }
    async update(id, updateReminderDto, currentUser) {
        if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only patients, doctors, hospitals, and admins can update reminders');
        }
        const supabase = this.supabaseService.getAdminClient();
        const existingReminder = await this.findOne(id, currentUser);
        if (updateReminderDto.scheduled_time) {
            const scheduledTime = new Date(updateReminderDto.scheduled_time);
            if (scheduledTime <= new Date()) {
                throw new common_1.BadRequestException('Scheduled time must be in the future');
            }
        }
        const { data, error } = await supabase
            .from('reminders')
            .update({
            ...(updateReminderDto.reminder_type && { reminder_type: updateReminderDto.reminder_type }),
            ...(updateReminderDto.scheduled_time && { scheduled_time: updateReminderDto.scheduled_time }),
            ...(updateReminderDto.message !== undefined && { message: updateReminderDto.message }),
            ...(updateReminderDto.is_recurring !== undefined && { is_recurring: updateReminderDto.is_recurring }),
            ...(updateReminderDto.recurrence_pattern && { recurrence_pattern: updateReminderDto.recurrence_pattern }),
            ...(updateReminderDto.recurrence_interval && { recurrence_interval: updateReminderDto.recurrence_interval }),
            ...(updateReminderDto.recurrence_days && { recurrence_days: updateReminderDto.recurrence_days }),
            ...(updateReminderDto.end_date !== undefined && { end_date: updateReminderDto.end_date }),
            ...(updateReminderDto.is_active !== undefined && { is_active: updateReminderDto.is_active }),
        })
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to update reminder: ${error.message}`);
        }
        return data;
    }
    async remove(id, currentUser) {
        if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only patients, doctors, hospitals, and admins can delete reminders');
        }
        const supabase = this.supabaseService.getAdminClient();
        await this.findOne(id, currentUser);
        const { error } = await supabase
            .from('reminders')
            .delete()
            .eq('id', id);
        if (error) {
            throw new common_1.BadRequestException(`Failed to delete reminder: ${error.message}`);
        }
    }
    async createBulkReminders(bulkReminderDto, currentUser) {
        if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only doctors, hospitals, and admins can create bulk reminders');
        }
        const supabase = this.supabaseService.getAdminClient();
        const reminders = [];
        const { data: medicine, error: medicineError } = await supabase
            .from('medicines')
            .select('id')
            .eq('id', bulkReminderDto.medicine_id)
            .single();
        if (medicineError || !medicine) {
            throw new common_1.NotFoundException('Medicine not found');
        }
        for (const patientId of bulkReminderDto.patient_ids) {
            try {
                const today = new Date();
                const [hours, minutes] = bulkReminderDto.time.split(':');
                const scheduledTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), parseInt(hours), parseInt(minutes));
                if (scheduledTime <= new Date()) {
                    scheduledTime.setDate(scheduledTime.getDate() + 1);
                }
                const reminderData = {
                    patient_id: patientId,
                    medicine_id: bulkReminderDto.medicine_id,
                    reminder_type: bulkReminderDto.reminder_type,
                    scheduled_time: scheduledTime.toISOString(),
                    message: bulkReminderDto.message,
                    is_recurring: bulkReminderDto.is_recurring,
                    recurrence_pattern: bulkReminderDto.recurrence_pattern,
                };
                const reminder = await this.create(reminderData, currentUser);
                reminders.push(reminder);
            }
            catch (error) {
                this.logger.warn(`Failed to create reminder for patient ${patientId}:`, error);
            }
        }
        return reminders;
    }
    async processPendingReminders() {
        this.logger.debug('Processing pending reminders...');
        const supabase = this.supabaseService.getAdminClient();
        const now = new Date();
        const { data: dueReminders, error } = await supabase
            .from('reminders')
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
            .eq('status', 'pending')
            .eq('is_active', true)
            .lte('scheduled_time', now.toISOString());
        if (error) {
            this.logger.error('Failed to fetch due reminders:', error);
            return;
        }
        if (!dueReminders || dueReminders.length === 0) {
            return;
        }
        this.logger.log(`Found ${dueReminders.length} due reminders`);
        for (const reminder of dueReminders) {
            try {
                await this.processReminder(reminder);
            }
            catch (error) {
                this.logger.error(`Failed to process reminder ${reminder.id}:`, error);
            }
        }
    }
    async processReminder(reminder) {
        const supabase = this.supabaseService.getAdminClient();
        try {
            this.logger.log(`Processing reminder ${reminder.id} for patient ${reminder.patient_id}`);
            const patientName = reminder.patient?.users?.name || 'Patient';
            const patientEmail = reminder.patient?.users?.email || '';
            const medicineName = reminder.medicine?.name || 'your medication';
            const dosage = reminder.medicine?.dosage || '';
            this.eventEmitter.emit('reminder.due', {
                userId: reminder.patient_id,
                reminderId: reminder.id,
                medicineId: reminder.medicine_id,
                medicineName,
                dosage,
                scheduledTime: new Date(reminder.scheduled_time),
            });
            const { data: patientDetails } = await supabase
                .from('patients')
                .select('phone_number')
                .eq('id', reminder.patient_id)
                .single();
            const phoneNumber = patientDetails?.phone_number;
            let reminderSent = false;
            let errorMessage = '';
            if (reminder.reminder_type === 'sms' && phoneNumber) {
                const smsResult = await this.twilioService.sendMedicationReminder(phoneNumber, patientName, medicineName, dosage, 'sms');
                reminderSent = smsResult.success;
                if (!smsResult.success) {
                    errorMessage = smsResult.error || 'SMS failed';
                }
            }
            else if (reminder.reminder_type === 'call' && phoneNumber) {
                const elevenLabsStatus = this.elevenLabsService.getServiceStatus();
                if (elevenLabsStatus.enabled && elevenLabsStatus.agentConfigured) {
                    const callResult = await this.elevenLabsService.makeConversationalCall({
                        phoneNumber,
                        patientName,
                        medicineName,
                        dosage,
                        patientId: reminder.patient_id,
                        medicineId: reminder.medicine_id,
                        reminderId: reminder.id,
                    });
                    reminderSent = callResult.success;
                    if (!callResult.success) {
                        errorMessage = callResult.error || 'ElevenLabs call failed';
                    }
                }
                else {
                    const callResult = await this.twilioService.sendMedicationReminder(phoneNumber, patientName, medicineName, dosage, 'call');
                    reminderSent = callResult.success;
                    if (!callResult.success) {
                        errorMessage = callResult.error || 'Twilio call failed';
                    }
                }
            }
            else if (reminder.reminder_type === 'email' && patientEmail) {
                this.logger.log(`Email reminder not implemented yet for ${patientEmail}`);
                reminderSent = false;
                errorMessage = 'Email reminders not implemented';
            }
            else {
                errorMessage = 'No valid contact method available';
            }
            const status = reminderSent ? 'sent' : 'failed';
            await supabase
                .from('reminders')
                .update({ status })
                .eq('id', reminder.id);
            await supabase
                .from('reminder_logs')
                .insert({
                reminder_id: reminder.id,
                status,
                message: reminderSent
                    ? `Reminder sent successfully via ${reminder.reminder_type}`
                    : `Failed to send reminder: ${errorMessage}`,
            });
            if (reminderSent) {
                this.eventEmitter.emit('reminder.sent', {
                    userId: reminder.patient_id,
                    reminderId: reminder.id,
                    medicineName,
                    type: reminder.reminder_type,
                    timestamp: new Date(),
                });
            }
            else {
                this.eventEmitter.emit('reminder.failed', {
                    userId: reminder.patient_id,
                    reminderId: reminder.id,
                    medicineName,
                    type: reminder.reminder_type,
                    error: errorMessage,
                    timestamp: new Date(),
                });
            }
            if (reminder.is_recurring && reminderSent) {
                await this.createNextRecurringReminder(reminder);
            }
        }
        catch (error) {
            this.logger.error(`Failed to process reminder ${reminder.id}:`, error);
            await supabase
                .from('reminders')
                .update({ status: 'failed' })
                .eq('id', reminder.id);
            await supabase
                .from('reminder_logs')
                .insert({
                reminder_id: reminder.id,
                status: 'failed',
                error_message: error.message,
            });
            throw error;
        }
    }
    async createNextRecurringReminder(reminder) {
        const supabase = this.supabaseService.getAdminClient();
        const currentTime = new Date(reminder.scheduled_time);
        let nextTime;
        switch (reminder.recurrence_pattern) {
            case 'daily':
                nextTime = new Date(currentTime.getTime() + (reminder.recurrence_interval * 24 * 60 * 60 * 1000));
                break;
            case 'weekly':
                nextTime = new Date(currentTime.getTime() + (reminder.recurrence_interval * 7 * 24 * 60 * 60 * 1000));
                break;
            case 'monthly':
                nextTime = new Date(currentTime);
                nextTime.setMonth(nextTime.getMonth() + reminder.recurrence_interval);
                break;
            default:
                return;
        }
        if (reminder.end_date && nextTime > new Date(reminder.end_date)) {
            return;
        }
        await supabase
            .from('reminders')
            .insert({
            patient_id: reminder.patient_id,
            medicine_id: reminder.medicine_id,
            reminder_type: reminder.reminder_type,
            scheduled_time: nextTime.toISOString(),
            message: reminder.message,
            is_recurring: reminder.is_recurring,
            recurrence_pattern: reminder.recurrence_pattern,
            recurrence_interval: reminder.recurrence_interval,
            recurrence_days: reminder.recurrence_days,
            end_date: reminder.end_date,
            status: 'pending',
            is_active: true,
        });
    }
};
exports.RemindersService = RemindersService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RemindersService.prototype, "processPendingReminders", null);
exports.RemindersService = RemindersService = RemindersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        twilio_service_1.TwilioService,
        elevenlabs_service_1.ElevenLabsService,
        event_emitter_1.EventEmitter2])
], RemindersService);
//# sourceMappingURL=reminders.service.js.map