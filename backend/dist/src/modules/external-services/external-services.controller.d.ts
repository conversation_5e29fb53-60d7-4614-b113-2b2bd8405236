import { ExternalServicesService } from './external-services.service';
export declare class TestSMSDto {
    phoneNumber: string;
    message: string;
}
export declare class TestCallDto {
    phoneNumber: string;
    message: string;
}
export declare class TestElevenLabsDto {
    phoneNumber: string;
    patientName: string;
    medicineName: string;
    dosage: string;
}
export declare class TestOpenAiDto {
    prescriptionText: string;
}
export declare class ExternalServicesController {
    private readonly externalServicesService;
    constructor(externalServicesService: ExternalServicesService);
    getServicesStatus(): Promise<{
        twilio: {
            description: string;
            enabled: boolean;
            configured: boolean;
        };
        elevenlabs: {
            description: string;
            enabled: boolean;
            configured: boolean;
            agentConfigured: boolean;
            voiceId: string;
        };
        aws: {
            s3: {
                enabled: boolean;
                description: string;
            };
            textract: {
                enabled: boolean;
                description: string;
            };
        };
        integration: {
            reminders: {
                sms: boolean;
                calls: boolean;
                conversational_ai: boolean;
            };
        };
    }>;
    testSMS(testSMSDto: TestSMSDto, req: any): Promise<{
        success: boolean;
        error: string;
    } | {
        timestamp: string;
        success: boolean;
        messageSid?: string;
        error?: string;
        service: string;
    }>;
    testCall(testCallDto: TestCallDto, req: any): Promise<{
        success: boolean;
        error: string;
    } | {
        timestamp: string;
        success: boolean;
        callSid?: string;
        error?: string;
        service: string;
    }>;
    testElevenLabs(testElevenLabsDto: TestElevenLabsDto, req: any): Promise<{
        success: boolean;
        error: string;
    } | {
        timestamp: string;
        success: boolean;
        callId?: string;
        error?: string;
        service: string;
    }>;
    getAWSStatus(): Promise<{
        s3: {
            enabled: boolean;
            configured: boolean;
            description: string;
        };
        textract: {
            enabled: boolean;
            configured: boolean;
            description: string;
        };
    }>;
    testTextract(req: any): Promise<{
        service: string;
        success: boolean;
        result: {
            extractedText: string;
            confidence: number;
            medicineInfo: any[];
        };
        timestamp: string;
        error?: undefined;
    } | {
        service: string;
        success: boolean;
        error: any;
        timestamp: string;
        result?: undefined;
    }>;
    testOpenAi(testData: TestOpenAiDto, req: any): Promise<{
        success: boolean;
        medicines: string[];
        error: string | undefined;
        testInput: string;
    } | {
        success: boolean;
        error: any;
        medicines?: undefined;
        testInput?: undefined;
    }>;
}
