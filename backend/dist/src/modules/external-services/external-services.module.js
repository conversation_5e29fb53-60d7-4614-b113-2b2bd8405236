"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalServicesModule = void 0;
const common_1 = require("@nestjs/common");
const external_services_controller_1 = require("./external-services.controller");
const external_services_service_1 = require("./external-services.service");
const twilio_service_1 = require("../../common/services/twilio.service");
const elevenlabs_service_1 = require("../../common/services/elevenlabs.service");
const aws_service_1 = require("../../common/services/aws.service");
const openai_service_1 = require("../../common/services/openai.service");
let ExternalServicesModule = class ExternalServicesModule {
};
exports.ExternalServicesModule = ExternalServicesModule;
exports.ExternalServicesModule = ExternalServicesModule = __decorate([
    (0, common_1.Module)({
        controllers: [external_services_controller_1.ExternalServicesController],
        providers: [external_services_service_1.ExternalServicesService, twilio_service_1.TwilioService, elevenlabs_service_1.ElevenLabsService, aws_service_1.AwsService, openai_service_1.OpenAiService],
        exports: [external_services_service_1.ExternalServicesService, twilio_service_1.TwilioService, elevenlabs_service_1.ElevenLabsService, aws_service_1.AwsService, openai_service_1.OpenAiService],
    })
], ExternalServicesModule);
//# sourceMappingURL=external-services.module.js.map