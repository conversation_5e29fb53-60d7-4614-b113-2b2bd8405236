"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalServicesController = exports.TestOpenAiDto = exports.TestElevenLabsDto = exports.TestCallDto = exports.TestSMSDto = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const external_services_service_1 = require("./external-services.service");
class TestSMSDto {
    phoneNumber;
    message;
}
exports.TestSMSDto = TestSMSDto;
class TestCallDto {
    phoneNumber;
    message;
}
exports.TestCallDto = TestCallDto;
class TestElevenLabsDto {
    phoneNumber;
    patientName;
    medicineName;
    dosage;
}
exports.TestElevenLabsDto = TestElevenLabsDto;
class TestOpenAiDto {
    prescriptionText;
}
exports.TestOpenAiDto = TestOpenAiDto;
let ExternalServicesController = class ExternalServicesController {
    externalServicesService;
    constructor(externalServicesService) {
        this.externalServicesService = externalServicesService;
    }
    async getServicesStatus() {
        return this.externalServicesService.getServicesStatus();
    }
    async testSMS(testSMSDto, req) {
        if (req.user.role !== 'admin') {
            throw new Error('Only admins can test external services');
        }
        return this.externalServicesService.testSMS(testSMSDto.phoneNumber, testSMSDto.message);
    }
    async testCall(testCallDto, req) {
        if (req.user.role !== 'admin') {
            throw new Error('Only admins can test external services');
        }
        return this.externalServicesService.testCall(testCallDto.phoneNumber, testCallDto.message);
    }
    async testElevenLabs(testElevenLabsDto, req) {
        if (req.user.role !== 'admin') {
            throw new Error('Only admins can test external services');
        }
        return this.externalServicesService.testElevenLabsCall(testElevenLabsDto.phoneNumber, testElevenLabsDto.patientName, testElevenLabsDto.medicineName, testElevenLabsDto.dosage);
    }
    async getAWSStatus() {
        return this.externalServicesService.getAWSStatus();
    }
    async testTextract(req) {
        if (req.user.role !== 'admin') {
            throw new Error('Only admins can test external services');
        }
        return this.externalServicesService.testTextract();
    }
    async testOpenAi(testData, req) {
        if (req.user.role !== 'admin') {
            throw new Error('Only admins can test external services');
        }
        return this.externalServicesService.testOpenAi(testData.prescriptionText);
    }
};
exports.ExternalServicesController = ExternalServicesController;
__decorate([
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get status of all external services' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'External services status retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ExternalServicesController.prototype, "getServicesStatus", null);
__decorate([
    (0, common_1.Post)('test/sms'),
    (0, swagger_1.ApiOperation)({ summary: 'Test SMS functionality' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'SMS test completed' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [TestSMSDto, Object]),
    __metadata("design:returntype", Promise)
], ExternalServicesController.prototype, "testSMS", null);
__decorate([
    (0, common_1.Post)('test/call'),
    (0, swagger_1.ApiOperation)({ summary: 'Test voice call functionality' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Call test completed' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [TestCallDto, Object]),
    __metadata("design:returntype", Promise)
], ExternalServicesController.prototype, "testCall", null);
__decorate([
    (0, common_1.Post)('test/elevenlabs'),
    (0, swagger_1.ApiOperation)({ summary: 'Test ElevenLabs conversational AI' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'ElevenLabs test completed' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [TestElevenLabsDto, Object]),
    __metadata("design:returntype", Promise)
], ExternalServicesController.prototype, "testElevenLabs", null);
__decorate([
    (0, common_1.Get)('aws/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get AWS services status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'AWS services status retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ExternalServicesController.prototype, "getAWSStatus", null);
__decorate([
    (0, common_1.Post)('test/textract'),
    (0, swagger_1.ApiOperation)({ summary: 'Test AWS Textract functionality' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Textract test completed' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ExternalServicesController.prototype, "testTextract", null);
__decorate([
    (0, common_1.Post)('test-openai'),
    (0, swagger_1.ApiOperation)({ summary: 'Test OpenAI medicine extraction functionality' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'OpenAI test completed' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [TestOpenAiDto, Object]),
    __metadata("design:returntype", Promise)
], ExternalServicesController.prototype, "testOpenAi", null);
exports.ExternalServicesController = ExternalServicesController = __decorate([
    (0, swagger_1.ApiTags)('external-services'),
    (0, common_1.Controller)('external-services'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [external_services_service_1.ExternalServicesService])
], ExternalServicesController);
//# sourceMappingURL=external-services.controller.js.map