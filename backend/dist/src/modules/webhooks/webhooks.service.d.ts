import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../../config/supabase.service';
import { OpenAiService } from '../../common/services/openai.service';
import { AdherenceService } from '../adherence/adherence.service';
export interface ElevenLabsWebhookPayload {
    type: string;
    event_timestamp: number;
    data: {
        conversation_id: string;
        agent_id: string;
        status: string;
        transcript: Array<{
            role: string;
            message: string;
            time_in_call_secs: number;
        }>;
        analysis: {
            transcript_summary: string;
            call_successful: string;
        };
        metadata: {
            start_time_unix_secs: number;
            call_duration_secs: number;
            cost: number;
        };
        conversation_initiation_client_data?: {
            dynamic_variables?: {
                user_name?: string;
                patient_id?: string;
                medicine_id?: string;
                reminder_id?: string;
                medicine_name?: string;
                dosage?: string;
            };
        };
    };
}
export declare class WebhooksService {
    private readonly configService;
    private readonly supabaseService;
    private readonly openAiService;
    private readonly adherenceService;
    private readonly logger;
    private readonly webhookSecret;
    constructor(configService: ConfigService, supabaseService: SupabaseService, openAiService: OpenAiService, adherenceService: AdherenceService);
    validateWebhookSignature(payload: string, signature: string, timestamp: string): boolean;
    processElevenLabsWebhook(payload: ElevenLabsWebhookPayload): Promise<{
        success: boolean;
        message: string;
        adherenceRecordCreated?: boolean;
        error?: string;
    }>;
    private storeConversationLog;
    private createAdherenceRecord;
}
