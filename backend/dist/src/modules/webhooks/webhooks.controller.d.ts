import { RawBodyRequest } from '@nestjs/common';
import { WebhooksService, ElevenLabsWebhookPayload } from './webhooks.service';
import { Request } from 'express';
export declare class WebhooksController {
    private readonly webhooksService;
    private readonly logger;
    constructor(webhooksService: WebhooksService);
    handleElevenLabsWebhook(req: RawBodyRequest<Request>, signature: string, timestamp: string, payload: ElevenLabsWebhookPayload): Promise<{
        success: boolean;
        message: string;
        adherenceRecordCreated: boolean;
    }>;
    testElevenLabsWebhook(): Promise<{
        success: boolean;
        message: string;
        result: {
            success: boolean;
            message: string;
            adherenceRecordCreated?: boolean;
            error?: string;
        };
    }>;
}
