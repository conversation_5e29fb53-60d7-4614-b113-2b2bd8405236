"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WebhooksController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhooksController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const webhooks_service_1 = require("./webhooks.service");
let WebhooksController = WebhooksController_1 = class WebhooksController {
    webhooksService;
    logger = new common_1.Logger(WebhooksController_1.name);
    constructor(webhooksService) {
        this.webhooksService = webhooksService;
    }
    async handleElevenLabsWebhook(req, signature, timestamp, payload) {
        try {
            this.logger.log('Received ElevenLabs webhook');
            const rawBody = req.rawBody?.toString() || JSON.stringify(payload);
            if (signature) {
                const isValidSignature = this.webhooksService.validateWebhookSignature(rawBody, signature, timestamp);
                if (!isValidSignature) {
                    this.logger.error('Invalid webhook signature');
                    throw new common_1.HttpException('Invalid webhook signature', common_1.HttpStatus.UNAUTHORIZED);
                }
            }
            else {
                this.logger.warn('No signature provided for webhook validation');
            }
            const result = await this.webhooksService.processElevenLabsWebhook(payload);
            if (!result.success) {
                this.logger.error(`Webhook processing failed: ${result.message}`);
                throw new common_1.HttpException(result.message || 'Failed to process webhook', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`Webhook processed successfully: ${result.message}`);
            return {
                success: true,
                message: result.message,
                adherenceRecordCreated: result.adherenceRecordCreated || false,
            };
        }
        catch (error) {
            this.logger.error('Error handling ElevenLabs webhook:', error);
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Internal server error processing webhook', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async testElevenLabsWebhook() {
        const testPayload = {
            type: 'post_call_transcription',
            event_timestamp: Math.floor(Date.now() / 1000),
            data: {
                conversation_id: 'test-conversation-123',
                agent_id: 'test-agent-456',
                status: 'done',
                transcript: [
                    {
                        role: 'agent',
                        message: 'Hello! This is a reminder to take your medication. Have you taken your Aspirin today?',
                        time_in_call_secs: 0,
                    },
                    {
                        role: 'user',
                        message: 'Yes, I took it this morning with breakfast.',
                        time_in_call_secs: 5,
                    },
                    {
                        role: 'agent',
                        message: 'Great! Thank you for taking your medication as prescribed. Have a wonderful day!',
                        time_in_call_secs: 8,
                    },
                ],
                analysis: {
                    transcript_summary: 'Patient confirmed taking their Aspirin medication as prescribed.',
                    call_successful: 'success',
                },
                metadata: {
                    start_time_unix_secs: Math.floor(Date.now() / 1000) - 30,
                    call_duration_secs: 15,
                    cost: 150,
                },
                conversation_initiation_client_data: {
                    dynamic_variables: {
                        user_name: 'Test Patient',
                        patient_id: 'test-patient-123',
                        medicine_id: 'test-medicine-456',
                        reminder_id: 'test-reminder-789',
                        medicine_name: 'Aspirin',
                        dosage: '100mg',
                    },
                },
            },
        };
        this.logger.log('Processing test ElevenLabs webhook');
        const result = await this.webhooksService.processElevenLabsWebhook(testPayload);
        return {
            success: true,
            message: 'Test webhook processed',
            result,
        };
    }
};
exports.WebhooksController = WebhooksController;
__decorate([
    (0, common_1.Post)('elevenlabs'),
    (0, swagger_1.ApiOperation)({
        summary: 'ElevenLabs post-call webhook',
        description: 'Receives post-call webhook from ElevenLabs conversational AI with conversation transcript and analysis'
    }),
    (0, swagger_1.ApiHeader)({
        name: 'elevenlabs-signature',
        description: 'HMAC signature for webhook validation',
        required: true,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Webhook processed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean' },
                message: { type: 'string' },
                adherenceRecordCreated: { type: 'boolean' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid webhook payload or signature',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Invalid webhook signature',
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Headers)('elevenlabs-signature')),
    __param(2, (0, common_1.Headers)('x-timestamp')),
    __param(3, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, Object]),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "handleElevenLabsWebhook", null);
__decorate([
    (0, common_1.Post)('test/elevenlabs'),
    (0, swagger_1.ApiOperation)({
        summary: 'Test ElevenLabs webhook processing',
        description: 'Test endpoint for ElevenLabs webhook processing with sample data'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Test webhook processed successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "testElevenLabsWebhook", null);
exports.WebhooksController = WebhooksController = WebhooksController_1 = __decorate([
    (0, swagger_1.ApiTags)('webhooks'),
    (0, common_1.Controller)('webhooks'),
    __metadata("design:paramtypes", [webhooks_service_1.WebhooksService])
], WebhooksController);
//# sourceMappingURL=webhooks.controller.js.map