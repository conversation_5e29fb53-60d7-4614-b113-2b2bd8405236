"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WebhooksService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhooksService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_service_1 = require("../../config/supabase.service");
const openai_service_1 = require("../../common/services/openai.service");
const adherence_service_1 = require("../adherence/adherence.service");
const dto_1 = require("../adherence/dto");
const crypto = require("crypto");
let WebhooksService = WebhooksService_1 = class WebhooksService {
    configService;
    supabaseService;
    openAiService;
    adherenceService;
    logger = new common_1.Logger(WebhooksService_1.name);
    webhookSecret;
    constructor(configService, supabaseService, openAiService, adherenceService) {
        this.configService = configService;
        this.supabaseService = supabaseService;
        this.openAiService = openAiService;
        this.adherenceService = adherenceService;
        this.webhookSecret = this.configService.get('ELEVENLABS_WEBHOOK_SECRET') || '';
        if (!this.webhookSecret) {
            this.logger.warn('ElevenLabs webhook secret not configured - webhook validation disabled');
        }
    }
    validateWebhookSignature(payload, signature, timestamp) {
        if (!this.webhookSecret) {
            this.logger.warn('Webhook secret not configured - skipping validation');
            return true;
        }
        try {
            const parts = signature.split(',');
            const timestampPart = parts.find(part => part.startsWith('t='));
            const hashPart = parts.find(part => part.startsWith('v0='));
            if (!timestampPart || !hashPart) {
                this.logger.error('Invalid signature format');
                return false;
            }
            const receivedTimestamp = timestampPart.split('=')[1];
            const receivedHash = hashPart.split('=')[1];
            const currentTime = Math.floor(Date.now() / 1000);
            const timestampDiff = currentTime - parseInt(receivedTimestamp);
            if (timestampDiff > 300) {
                this.logger.error('Webhook timestamp too old');
                return false;
            }
            const fullPayloadToSign = `${receivedTimestamp}.${payload}`;
            const expectedHash = crypto
                .createHmac('sha256', this.webhookSecret)
                .update(fullPayloadToSign)
                .digest('hex');
            const isValid = crypto.timingSafeEqual(Buffer.from(receivedHash, 'hex'), Buffer.from(expectedHash, 'hex'));
            if (!isValid) {
                this.logger.error('Invalid webhook signature');
            }
            return isValid;
        }
        catch (error) {
            this.logger.error('Error validating webhook signature:', error);
            return false;
        }
    }
    async processElevenLabsWebhook(payload) {
        try {
            this.logger.log(`Processing ElevenLabs webhook for conversation: ${payload.data.conversation_id}`);
            if (payload.type !== 'post_call_transcription') {
                return {
                    success: false,
                    message: `Unsupported webhook type: ${payload.type}`
                };
            }
            if (!payload.data.transcript || payload.data.transcript.length === 0) {
                return {
                    success: false,
                    message: 'No transcript data in webhook payload'
                };
            }
            const conversationData = payload.data;
            const dynamicVars = conversationData.conversation_initiation_client_data?.dynamic_variables;
            if (!dynamicVars?.patient_id || !dynamicVars?.medicine_id || !dynamicVars?.reminder_id) {
                this.logger.warn('Missing required dynamic variables in conversation data');
                return {
                    success: true,
                    message: 'Conversation processed but missing metadata for adherence tracking',
                    adherenceRecordCreated: false
                };
            }
            const analysis = await this.openAiService.analyzeConversationForMedicineIntake(conversationData.transcript, dynamicVars.user_name || 'Patient', dynamicVars.medicine_name || 'Medicine', dynamicVars.dosage || 'Unknown dosage');
            if (!analysis.success) {
                this.logger.error('Failed to analyze conversation:', analysis.error);
                return {
                    success: false,
                    message: 'Failed to analyze conversation',
                    error: analysis.error
                };
            }
            this.logger.log(`Conversation analysis result: ${analysis.medicineConfirmed ? 'CONFIRMED' : 'NOT CONFIRMED'} (${analysis.confidence}% confidence)`);
            await this.storeConversationLog(payload, analysis);
            let adherenceRecordCreated = false;
            if (analysis.medicineConfirmed && analysis.confidence >= 70) {
                try {
                    await this.createAdherenceRecord(dynamicVars.patient_id, dynamicVars.medicine_id, dynamicVars.reminder_id, conversationData, analysis);
                    adherenceRecordCreated = true;
                    this.logger.log(`Adherence record created for patient ${dynamicVars.patient_id}`);
                }
                catch (error) {
                    this.logger.error('Failed to create adherence record:', error);
                }
            }
            return {
                success: true,
                message: `Conversation processed successfully. Medicine intake ${analysis.medicineConfirmed ? 'confirmed' : 'not confirmed'}.`,
                adherenceRecordCreated
            };
        }
        catch (error) {
            this.logger.error('Error processing ElevenLabs webhook:', error);
            return {
                success: false,
                message: 'Internal error processing webhook',
                error: error.message
            };
        }
    }
    async storeConversationLog(payload, analysis) {
        const supabase = this.supabaseService.getAdminClient();
        const { error } = await supabase
            .from('conversation_logs')
            .insert({
            conversation_id: payload.data.conversation_id,
            agent_id: payload.data.agent_id,
            patient_id: payload.data.conversation_initiation_client_data?.dynamic_variables?.patient_id,
            medicine_id: payload.data.conversation_initiation_client_data?.dynamic_variables?.medicine_id,
            reminder_id: payload.data.conversation_initiation_client_data?.dynamic_variables?.reminder_id,
            transcript: payload.data.transcript,
            call_duration_secs: payload.data.metadata.call_duration_secs,
            call_cost: payload.data.metadata.cost,
            transcript_summary: payload.data.analysis.transcript_summary,
            call_successful: payload.data.analysis.call_successful,
            medicine_confirmed: analysis.medicineConfirmed,
            confidence_score: analysis.confidence,
            ai_reasoning: analysis.reasoning,
            created_at: new Date().toISOString(),
        });
        if (error) {
            this.logger.error('Failed to store conversation log:', error);
            throw new Error(`Failed to store conversation log: ${error.message}`);
        }
    }
    async createAdherenceRecord(patientId, medicineId, reminderId, conversationData, analysis) {
        const supabase = this.supabaseService.getAdminClient();
        const { data: reminder, error: reminderError } = await supabase
            .from('reminders')
            .select('scheduled_time')
            .eq('id', reminderId)
            .single();
        if (reminderError) {
            throw new Error(`Failed to get reminder details: ${reminderError.message}`);
        }
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('id, email, name, role, created_at, updated_at')
            .eq('id', patientId)
            .single();
        if (userError) {
            throw new Error(`Failed to get user details: ${userError.message}`);
        }
        const adherenceData = {
            patient_id: patientId,
            medicine_id: medicineId,
            scheduled_time: reminder.scheduled_time,
            taken_time: new Date().toISOString(),
            status: dto_1.AdherenceStatus.TAKEN,
            notes: `Confirmed via AI conversation (${analysis.confidence}% confidence): ${analysis.reasoning}`,
        };
        await this.adherenceService.create(adherenceData, user);
    }
};
exports.WebhooksService = WebhooksService;
exports.WebhooksService = WebhooksService = WebhooksService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        supabase_service_1.SupabaseService,
        openai_service_1.OpenAiService,
        adherence_service_1.AdherenceService])
], WebhooksService);
//# sourceMappingURL=webhooks.service.js.map