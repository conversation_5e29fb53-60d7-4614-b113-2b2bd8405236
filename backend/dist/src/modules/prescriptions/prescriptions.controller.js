"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrescriptionsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const prescriptions_service_1 = require("./prescriptions.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let PrescriptionsController = class PrescriptionsController {
    prescriptionsService;
    constructor(prescriptionsService) {
        this.prescriptionsService = prescriptionsService;
    }
    async create(createPrescriptionDto, currentUser) {
        return this.prescriptionsService.create(createPrescriptionDto, currentUser);
    }
    async uploadPrescription(file, patientId, patientIdAlt, doctorId, currentUser) {
        const finalPatientId = patientId || patientIdAlt || (currentUser.role === 'patient' ? currentUser.id : undefined);
        if (!finalPatientId) {
            throw new common_1.BadRequestException('Patient ID is required');
        }
        const result = await this.prescriptionsService.uploadAndProcessPrescription(file, finalPatientId, doctorId, currentUser);
        return this.transformPrescriptionResponse(result);
    }
    async findAll(query, currentUser) {
        const prescriptions = await this.prescriptionsService.findAll(query, currentUser);
        return prescriptions.map(prescription => this.transformPrescriptionResponse(prescription));
    }
    async findByPatient(patientId, currentUser) {
        return this.prescriptionsService.findByPatient(patientId, currentUser);
    }
    async findByDoctor(doctorId, currentUser) {
        return this.prescriptionsService.findByDoctor(doctorId, currentUser);
    }
    async findOne(id, currentUser) {
        return this.prescriptionsService.findOne(id, currentUser);
    }
    async update(id, updatePrescriptionDto, currentUser) {
        return this.prescriptionsService.update(id, updatePrescriptionDto, currentUser);
    }
    async processPrescription(id, processPrescriptionDto, currentUser) {
        return this.prescriptionsService.processPrescription(id, processPrescriptionDto, currentUser);
    }
    async remove(id, currentUser) {
        await this.prescriptionsService.remove(id, currentUser);
        return { message: 'Prescription deleted successfully' };
    }
    transformPrescriptionResponse(prescription) {
        return {
            id: prescription.id,
            patientId: prescription.patient_id,
            doctorId: prescription.doctor_id,
            prescriptionDate: prescription.prescription_date,
            filename: prescription.filename,
            fileUrl: prescription.file_url,
            extractedText: prescription.extracted_text,
            status: prescription.status,
            textractConfidence: prescription.textract_confidence,
            medicineInfo: prescription.medicine_info,
            aiExtractedMedicines: prescription.ai_extracted_medicines,
            aiExtractionSuccess: prescription.ai_extraction_success,
            errorMessage: prescription.error_message,
            createdAt: prescription.created_at,
            updatedAt: prescription.updated_at,
            medicines: prescription.medicines || [],
        };
    }
};
exports.PrescriptionsController = PrescriptionsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new prescription' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Prescription created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreatePrescriptionDto, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('upload'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload prescription file and extract text using AWS Textract' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Prescription uploaded and processed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)('patient_id')),
    __param(2, (0, common_1.Body)('patientId')),
    __param(3, (0, common_1.Body)('doctor_id')),
    __param(4, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "uploadPrescription", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prescriptions' }),
    (0, swagger_1.ApiQuery)({ name: 'patient_id', required: false, description: 'Filter by patient ID' }),
    (0, swagger_1.ApiQuery)({ name: 'doctor_id', required: false, description: 'Filter by doctor ID' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: ['processing', 'completed', 'failed', 'all'], description: 'Filter by status' }),
    (0, swagger_1.ApiQuery)({ name: 'filename', required: false, description: 'Filter by filename' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Prescriptions retrieved successfully' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.PrescriptionQueryDto, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('patient/:patientId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get prescriptions for a specific patient' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Patient prescriptions retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('patientId', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "findByPatient", null);
__decorate([
    (0, common_1.Get)('doctor/:doctorId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get prescriptions for a specific doctor' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Doctor prescriptions retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Doctor not found' }),
    (0, roles_decorator_1.Roles)('doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('doctorId', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "findByDoctor", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get prescription by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Prescription retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Prescription not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update prescription' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Prescription updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Prescription not found' }),
    (0, roles_decorator_1.Roles)('doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdatePrescriptionDto, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/process'),
    (0, swagger_1.ApiOperation)({ summary: 'Process prescription (update status and extracted text)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Prescription processed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Prescription not found' }),
    (0, roles_decorator_1.Roles)('doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.ProcessPrescriptionDto, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "processPrescription", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete prescription' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Prescription deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Only administrators can delete prescriptions' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Prescription not found' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PrescriptionsController.prototype, "remove", null);
exports.PrescriptionsController = PrescriptionsController = __decorate([
    (0, swagger_1.ApiTags)('prescriptions'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('prescriptions'),
    __metadata("design:paramtypes", [prescriptions_service_1.PrescriptionsService])
], PrescriptionsController);
//# sourceMappingURL=prescriptions.controller.js.map