"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrescriptionsModule = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const prescriptions_service_1 = require("./prescriptions.service");
const prescriptions_controller_1 = require("./prescriptions.controller");
const supabase_service_1 = require("../../config/supabase.service");
const aws_service_1 = require("../../common/services/aws.service");
const file_upload_service_1 = require("../../common/services/file-upload.service");
const openai_service_1 = require("../../common/services/openai.service");
const medicines_service_1 = require("../medicines/medicines.service");
let PrescriptionsModule = class PrescriptionsModule {
};
exports.PrescriptionsModule = PrescriptionsModule;
exports.PrescriptionsModule = PrescriptionsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            platform_express_1.MulterModule.registerAsync({
                useFactory: () => ({
                    storage: require('multer').memoryStorage(),
                    limits: {
                        fileSize: 10 * 1024 * 1024,
                    },
                }),
            }),
        ],
        controllers: [prescriptions_controller_1.PrescriptionsController],
        providers: [prescriptions_service_1.PrescriptionsService, supabase_service_1.SupabaseService, aws_service_1.AwsService, file_upload_service_1.FileUploadService, openai_service_1.OpenAiService, medicines_service_1.MedicinesService],
        exports: [prescriptions_service_1.PrescriptionsService],
    })
], PrescriptionsModule);
//# sourceMappingURL=prescriptions.module.js.map