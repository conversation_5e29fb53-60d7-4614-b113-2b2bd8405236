"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PrescriptionsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrescriptionsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
const aws_service_1 = require("../../common/services/aws.service");
const file_upload_service_1 = require("../../common/services/file-upload.service");
const medicines_service_1 = require("../medicines/medicines.service");
let PrescriptionsService = PrescriptionsService_1 = class PrescriptionsService {
    supabaseService;
    awsService;
    fileUploadService;
    medicinesService;
    logger = new common_1.Logger(PrescriptionsService_1.name);
    constructor(supabaseService, awsService, fileUploadService, medicinesService) {
        this.supabaseService = supabaseService;
        this.awsService = awsService;
        this.fileUploadService = fileUploadService;
        this.medicinesService = medicinesService;
    }
    async create(createPrescriptionDto, currentUser) {
        if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only patients, doctors, hospitals, and admins can create prescriptions');
        }
        const supabase = this.supabaseService.getAdminClient();
        const { data: patient, error: patientError } = await supabase
            .from('patients')
            .select('id, assigned_doctor_id')
            .eq('id', createPrescriptionDto.patient_id)
            .single();
        if (patientError || !patient) {
            throw new common_1.NotFoundException('Patient not found');
        }
        if (currentUser.role === 'patient' && currentUser.id !== createPrescriptionDto.patient_id) {
            throw new common_1.ForbiddenException('You can only create prescriptions for yourself');
        }
        if (createPrescriptionDto.doctor_id) {
            const { data: doctor, error: doctorError } = await supabase
                .from('doctors')
                .select('id')
                .eq('id', createPrescriptionDto.doctor_id)
                .single();
            if (doctorError || !doctor) {
                throw new common_1.NotFoundException('Doctor not found');
            }
            if (currentUser.role === 'doctor' && currentUser.id !== createPrescriptionDto.doctor_id) {
                throw new common_1.ForbiddenException('You can only create prescriptions as yourself');
            }
        }
        const { data, error } = await supabase
            .from('prescriptions')
            .insert({
            patient_id: createPrescriptionDto.patient_id,
            doctor_id: createPrescriptionDto.doctor_id,
            filename: createPrescriptionDto.filename,
            file_url: createPrescriptionDto.file_url,
            extracted_text: createPrescriptionDto.extracted_text,
            status: 'processing',
        })
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to create prescription: ${error.message}`);
        }
        return data;
    }
    async findAll(query, currentUser) {
        const supabase = this.supabaseService.getClient();
        let dbQuery = supabase
            .from('prescriptions')
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        doctor:doctors(id, users!doctors_id_fkey(name, email), specialization),
        medicines(id, name, dosage, frequency, duration, start_date, end_date, instructions, side_effects)
      `);
        if (currentUser.role === 'patient') {
            dbQuery = dbQuery.eq('patient_id', currentUser.id);
        }
        else if (currentUser.role === 'doctor') {
            const { data: patientIds } = await supabase
                .from('patients')
                .select('id')
                .eq('assigned_doctor_id', currentUser.id);
            const ids = patientIds?.map(p => p.id) || [];
            if (ids.length > 0) {
                dbQuery = dbQuery.or(`doctor_id.eq.${currentUser.id},patient_id.in.(${ids.join(',')})`);
            }
            else {
                dbQuery = dbQuery.eq('doctor_id', currentUser.id);
            }
        }
        if (query.patient_id) {
            if (currentUser.role === 'patient' && query.patient_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view your own prescriptions');
            }
            dbQuery = dbQuery.eq('patient_id', query.patient_id);
        }
        if (query.doctor_id) {
            dbQuery = dbQuery.eq('doctor_id', query.doctor_id);
        }
        if (query.filename) {
            dbQuery = dbQuery.ilike('filename', `%${query.filename}%`);
        }
        if (query.status && query.status !== 'all') {
            dbQuery = dbQuery.eq('status', query.status);
        }
        const { data, error } = await dbQuery.order('created_at', { ascending: false });
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch prescriptions: ${error.message}`);
        }
        return data || [];
    }
    async findOne(id, currentUser) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('prescriptions')
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email), assigned_doctor_id),
        doctor:doctors(id, users!doctors_id_fkey(name, email), specialization),
        medicines(id, name, dosage, frequency, duration, start_date, end_date, instructions, side_effects)
      `)
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Prescription with ID ${id} not found`);
        }
        if (currentUser.role === 'patient' && data.patient_id !== currentUser.id) {
            throw new common_1.ForbiddenException('You can only view your own prescriptions');
        }
        else if (currentUser.role === 'doctor') {
            const hasAccess = data.doctor_id === currentUser.id ||
                data.patient?.assigned_doctor_id === currentUser.id;
            if (!hasAccess) {
                throw new common_1.ForbiddenException('You can only view prescriptions for your patients');
            }
        }
        return data;
    }
    async update(id, updatePrescriptionDto, currentUser) {
        if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only doctors, hospitals, and admins can update prescriptions');
        }
        const supabase = this.supabaseService.getAdminClient();
        const existingPrescription = await this.findOne(id, currentUser);
        if (updatePrescriptionDto.doctor_id) {
            const { data: doctor, error: doctorError } = await supabase
                .from('doctors')
                .select('id')
                .eq('id', updatePrescriptionDto.doctor_id)
                .single();
            if (doctorError || !doctor) {
                throw new common_1.NotFoundException('Doctor not found');
            }
        }
        const { data, error } = await supabase
            .from('prescriptions')
            .update({
            ...(updatePrescriptionDto.doctor_id !== undefined && { doctor_id: updatePrescriptionDto.doctor_id }),
            ...(updatePrescriptionDto.status && { status: updatePrescriptionDto.status }),
            ...(updatePrescriptionDto.extracted_text !== undefined && { extracted_text: updatePrescriptionDto.extracted_text }),
        })
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to update prescription: ${error.message}`);
        }
        return data;
    }
    async remove(id, currentUser) {
        if (currentUser.role !== 'admin') {
            throw new common_1.ForbiddenException('Only administrators can delete prescriptions');
        }
        const supabase = this.supabaseService.getAdminClient();
        await this.findOne(id, currentUser);
        const { error } = await supabase
            .from('prescriptions')
            .delete()
            .eq('id', id);
        if (error) {
            throw new common_1.BadRequestException(`Failed to delete prescription: ${error.message}`);
        }
    }
    async processPrescription(id, processPrescriptionDto, currentUser) {
        if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only doctors, hospitals, and admins can process prescriptions');
        }
        const supabase = this.supabaseService.getAdminClient();
        await this.findOne(id, currentUser);
        const { data, error } = await supabase
            .from('prescriptions')
            .update({
            status: processPrescriptionDto.status,
            extracted_text: processPrescriptionDto.extracted_text,
        })
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to process prescription: ${error.message}`);
        }
        return data;
    }
    async findByPatient(patientId, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== patientId) {
            throw new common_1.ForbiddenException('You can only view your own prescriptions');
        }
        return this.findAll({ patient_id: patientId }, currentUser);
    }
    async findByDoctor(doctorId, currentUser) {
        if (currentUser.role === 'doctor' && currentUser.id !== doctorId) {
            throw new common_1.ForbiddenException('You can only view your own prescriptions');
        }
        return this.findAll({ doctor_id: doctorId }, currentUser);
    }
    async uploadAndProcessPrescription(file, patientId, doctorId, currentUser) {
        this.fileUploadService.validateFile(file);
        if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
            throw new common_1.ForbiddenException('Only patients, doctors, hospitals, and admins can upload prescriptions');
        }
        const supabase = this.supabaseService.getAdminClient();
        let { data: patient, error: patientError } = await supabase
            .from('patients')
            .select('id, assigned_doctor_id')
            .eq('id', patientId)
            .single();
        if (patientError || !patient) {
            if (currentUser.role === 'patient' && currentUser.id === patientId) {
                const { data: newPatient, error: createError } = await supabase
                    .from('patients')
                    .insert({
                    id: patientId,
                    date_of_birth: null,
                    emergency_contact: null,
                })
                    .select('id, assigned_doctor_id')
                    .single();
                if (createError || !newPatient) {
                    this.logger.error('Failed to create patient record:', createError);
                    throw new common_1.NotFoundException('Failed to create patient record');
                }
                patient = newPatient;
                this.logger.log(`Created patient record for user ${patientId}`);
            }
            else {
                throw new common_1.NotFoundException('Patient not found');
            }
        }
        if (currentUser.role === 'patient' && currentUser.id !== patientId) {
            throw new common_1.ForbiddenException('You can only upload prescriptions for yourself');
        }
        try {
            const fileName = this.fileUploadService.generateFileName(file.originalname);
            const fileUrl = await this.awsService.uploadFileToS3(file.buffer, fileName, file.mimetype);
            const extractionResult = await this.awsService.extractTextFromPrescription(file.buffer, file.mimetype);
            const { data, error } = await supabase
                .from('prescriptions')
                .insert({
                patient_id: patientId,
                doctor_id: doctorId || null,
                prescription_date: new Date().toISOString().split('T')[0],
                filename: fileName,
                file_url: fileUrl,
                extracted_text: extractionResult.extractedText,
                status: 'completed',
                textract_confidence: extractionResult.confidence,
                medicine_info: extractionResult.medicineInfo,
                ai_extracted_medicines: extractionResult.aiExtractedMedicines || [],
                ai_extraction_success: extractionResult.aiExtractionSuccess || false,
            })
                .select()
                .single();
            if (error) {
                throw new common_1.BadRequestException(`Failed to create prescription: ${error.message}`);
            }
            if (extractionResult.aiExtractedMedicines && extractionResult.aiExtractedMedicines.length > 0) {
                try {
                    await this.medicinesService.createMedicinesFromExtractedNames(extractionResult.aiExtractedMedicines, data.id, patientId, currentUser);
                    this.logger.log(`Created ${extractionResult.aiExtractedMedicines.length} medicine records from extracted names`);
                }
                catch (medicineError) {
                    this.logger.error('Failed to create medicine records from extracted names:', medicineError);
                }
            }
            return data;
        }
        catch (error) {
            const fileName = this.fileUploadService.generateFileName(file.originalname);
            try {
                const fileUrl = await this.awsService.uploadFileToS3(file.buffer, fileName, file.mimetype);
                const { data, error: dbError } = await supabase
                    .from('prescriptions')
                    .insert({
                    patient_id: patientId,
                    doctor_id: doctorId || null,
                    prescription_date: new Date().toISOString().split('T')[0],
                    filename: fileName,
                    file_url: fileUrl,
                    extracted_text: null,
                    status: 'failed',
                    textract_confidence: 0,
                    error_message: error.message,
                })
                    .select()
                    .single();
                if (dbError) {
                    throw new common_1.BadRequestException(`Failed to create prescription: ${dbError.message}`);
                }
                return data;
            }
            catch (uploadError) {
                throw new common_1.BadRequestException(`Failed to upload and process prescription: ${uploadError.message}`);
            }
        }
    }
};
exports.PrescriptionsService = PrescriptionsService;
exports.PrescriptionsService = PrescriptionsService = PrescriptionsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        aws_service_1.AwsService,
        file_upload_service_1.FileUploadService,
        medicines_service_1.MedicinesService])
], PrescriptionsService);
//# sourceMappingURL=prescriptions.service.js.map