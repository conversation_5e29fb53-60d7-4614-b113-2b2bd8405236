const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testMedicineCreation() {
  console.log('🧪 Testing medicine creation...');
  
  try {
    // First, let's check if we have any test patients
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select('id')
      .limit(1);
    
    if (patientsError) {
      console.error('❌ Error fetching patients:', patientsError);
      return;
    }
    
    if (!patients || patients.length === 0) {
      console.log('⚠️ No patients found. Creating a test patient...');
      
      // Create a test user first
      const testUserId = '550e8400-e29b-41d4-a716-446655440000';
      const { error: userError } = await supabase
        .from('users')
        .upsert({
          id: testUserId,
          name: 'Test Patient',
          email: '<EMAIL>',
          role: 'patient'
        });
      
      if (userError) {
        console.error('❌ Error creating test user:', userError);
        return;
      }
      
      // Create a test patient
      const { data: newPatient, error: patientError } = await supabase
        .from('patients')
        .upsert({
          id: testUserId,
          date_of_birth: '1990-01-01',
          emergency_contact: '+**********'
        })
        .select()
        .single();
      
      if (patientError) {
        console.error('❌ Error creating test patient:', patientError);
        return;
      }
      
      console.log('✅ Created test patient:', newPatient.id);
      patients.push(newPatient);
    }
    
    const patientId = patients[0].id;
    console.log('👤 Using patient ID:', patientId);
    
    // Create a test prescription first
    const { data: prescription, error: prescriptionError } = await supabase
      .from('prescriptions')
      .insert({
        patient_id: patientId,
        prescription_date: new Date().toISOString().split('T')[0],
        filename: 'test-prescription.pdf',
        file_url: 'https://example.com/test.pdf',
        extracted_text: 'TAB. GLIZID M XR 60 MG\nLINERO 5 MG TABLET',
        status: 'completed',
        ai_extracted_medicines: ['TAB. GLIZID M XR 60 MG', 'LINERO 5 MG TABLET'],
        ai_extraction_success: true
      })
      .select()
      .single();
    
    if (prescriptionError) {
      console.error('❌ Error creating test prescription:', prescriptionError);
      return;
    }
    
    console.log('📋 Created test prescription:', prescription.id);
    
    // Now try to create medicines
    const testMedicines = [
      {
        name: 'GLIZID M XR',
        dosage: '60 MG',
        frequency: 'As prescribed',
        duration: '30',
        instructions: 'Take as directed by your doctor',
        start_date: new Date().toISOString().split('T')[0],
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        prescription_id: prescription.id,
        patient_id: patientId,
        is_active: true
      },
      {
        name: 'LINERO',
        dosage: '5 MG',
        frequency: 'As prescribed',
        duration: '30',
        instructions: 'Take as directed by your doctor',
        start_date: new Date().toISOString().split('T')[0],
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        prescription_id: prescription.id,
        patient_id: patientId,
        is_active: true
      }
    ];
    
    console.log('💊 Attempting to create medicines...');
    
    for (const medicine of testMedicines) {
      const { data, error } = await supabase
        .from('medicines')
        .insert(medicine)
        .select()
        .single();
      
      if (error) {
        console.error(`❌ Error creating medicine "${medicine.name}":`, error);
      } else {
        console.log(`✅ Created medicine: ${data.name} (${data.dosage})`);
      }
    }
    
    // Check if medicines were created
    const { data: createdMedicines, error: fetchError } = await supabase
      .from('medicines')
      .select('*')
      .eq('prescription_id', prescription.id);
    
    if (fetchError) {
      console.error('❌ Error fetching created medicines:', fetchError);
    } else {
      console.log(`✅ Total medicines created: ${createdMedicines.length}`);
      createdMedicines.forEach(med => {
        console.log(`   - ${med.name} (${med.dosage})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

testMedicineCreation();
