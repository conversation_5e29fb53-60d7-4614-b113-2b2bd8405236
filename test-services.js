const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';

async function createAdminUser() {
  try {
    // Register a new user as patient (admin role not allowed in registration)
    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
      name: 'Test Admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'patient'
    });

    console.log('User created as patient:', registerResponse.data);
    console.log('NOTE: You need to manually update this user\'s role to "admin" in the database');
    console.log('SQL: UPDATE users SET role = \'admin\' WHERE email = \'<EMAIL>\';');
    return registerResponse.data;
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('User already exists, trying to login...');
      return null;
    }
    console.error('Error creating user:', error.response?.data || error.message);
    throw error;
  }
}

async function loginAdmin() {
  try {
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    console.log('Admin login successful');

    // Decode JWT token to see what's inside
    const token = loginResponse.data.accessToken;
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    console.log('JWT Token Payload:', JSON.stringify(payload, null, 2));

    return token;
  } catch (error) {
    console.error('Error logging in admin:', error.response?.data || error.message);
    throw error;
  }
}

async function testExternalServices(token) {
  const headers = { Authorization: `Bearer ${token}` };

  try {
    // First check current user profile to see the role
    console.log('\n=== Checking Current User Profile ===');
    const profileResponse = await axios.get(`${BASE_URL}/auth/me`, { headers });
    console.log('Current User:', JSON.stringify(profileResponse.data, null, 2));

    // Test services status
    console.log('\n=== Testing Services Status ===');
    const statusResponse = await axios.get(`${BASE_URL}/external-services/status`, { headers });
    console.log('Services Status:', JSON.stringify(statusResponse.data, null, 2));
    
    // Test Twilio SMS
    console.log('\n=== Testing Twilio SMS ===');
    const smsResponse = await axios.post(`${BASE_URL}/external-services/test/sms`, {
      phoneNumber: '+919101004681',
      message: 'Test SMS from MedCare - ElevenLabs & Twilio Integration Test'
    }, { headers });
    console.log('SMS Test Result:', JSON.stringify(smsResponse.data, null, 2));

    // Test Twilio Call
    console.log('\n=== Testing Twilio Call ===');
    const callResponse = await axios.post(`${BASE_URL}/external-services/test/call`, {
      phoneNumber: '+919101004681',
      message: 'Hello, this is a test call from MedCare to verify Twilio integration.'
    }, { headers });
    console.log('Call Test Result:', JSON.stringify(callResponse.data, null, 2));

    // Test ElevenLabs Conversational AI
    console.log('\n=== Testing ElevenLabs Conversational AI ===');
    const elevenLabsResponse = await axios.post(`${BASE_URL}/external-services/test/elevenlabs`, {
      phoneNumber: '+919101004681',
      patientName: 'John Doe',
      medicineName: 'Aspirin',
      dosage: '100mg'
    }, { headers });
    console.log('ElevenLabs Test Result:', JSON.stringify(elevenLabsResponse.data, null, 2));
    
  } catch (error) {
    console.error('Error testing services:', error.response?.data || error.message);
  }
}

async function main() {
  try {
    // Create admin user
    await createAdminUser();

    // Wait a moment for role update to take effect
    console.log('Waiting for role update to take effect...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Login as admin to get fresh token with updated role
    const token = await loginAdmin();

    // Test external services
    await testExternalServices(token);

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

main();
