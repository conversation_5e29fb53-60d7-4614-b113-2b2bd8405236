const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';

async function createAdminUser() {
  try {
    // Register a new user (will be patient by default)
    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
      name: 'Test Admin',
      email: '<EMAIL>',
      password: 'admin123'
    });

    console.log('User created:', registerResponse.data);
    console.log('NOTE: You need to manually update this user\'s role to "admin" in the database');
    console.log('SQL: UPDATE users SET role = \'admin\' WHERE email = \'<EMAIL>\';');
    return registerResponse.data;
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('User already exists, trying to login...');
      return null;
    }
    console.error('Error creating user:', error.response?.data || error.message);
    throw error;
  }
}

async function loginAdmin() {
  try {
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('Admin login successful');
    return loginResponse.data.access_token;
  } catch (error) {
    console.error('Error logging in admin:', error.response?.data || error.message);
    throw error;
  }
}

async function testExternalServices(token) {
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // Test services status
    console.log('\n=== Testing Services Status ===');
    const statusResponse = await axios.get(`${BASE_URL}/external-services/status`, { headers });
    console.log('Services Status:', JSON.stringify(statusResponse.data, null, 2));
    
    // Test Twilio SMS
    console.log('\n=== Testing Twilio SMS ===');
    const smsResponse = await axios.post(`${BASE_URL}/external-services/test/sms`, {
      phoneNumber: '+**********', // Replace with your test number
      message: 'Test SMS from MedCare - ElevenLabs & Twilio Integration Test'
    }, { headers });
    console.log('SMS Test Result:', JSON.stringify(smsResponse.data, null, 2));
    
    // Test Twilio Call
    console.log('\n=== Testing Twilio Call ===');
    const callResponse = await axios.post(`${BASE_URL}/external-services/test/call`, {
      phoneNumber: '+**********', // Replace with your test number
      message: 'Hello, this is a test call from MedCare to verify Twilio integration.'
    }, { headers });
    console.log('Call Test Result:', JSON.stringify(callResponse.data, null, 2));
    
    // Test ElevenLabs Conversational AI
    console.log('\n=== Testing ElevenLabs Conversational AI ===');
    const elevenLabsResponse = await axios.post(`${BASE_URL}/external-services/test/elevenlabs`, {
      phoneNumber: '+**********', // Replace with your test number
      patientName: 'John Doe',
      medicineName: 'Aspirin',
      dosage: '100mg'
    }, { headers });
    console.log('ElevenLabs Test Result:', JSON.stringify(elevenLabsResponse.data, null, 2));
    
  } catch (error) {
    console.error('Error testing services:', error.response?.data || error.message);
  }
}

async function main() {
  try {
    // Create admin user
    await createAdminUser();
    
    // Login as admin
    const token = await loginAdmin();
    
    // Test external services
    await testExternalServices(token);
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

main();
