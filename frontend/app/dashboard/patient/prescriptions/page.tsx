'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { PrescriptionUpload } from '@/components/prescriptions/prescription-upload';
import { ManualMedicineModal } from '@/components/prescriptions/manual-medicine-modal';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useMedicineStore } from '@/lib/store';
import { FileText, Download, Eye, Clock, Upload, Sparkles, CheckCircle, AlertCircle, Plus, Pill } from 'lucide-react';
import { format } from 'date-fns';
import { useEffect, useState } from 'react';

export default function PrescriptionsPage() {
  const { prescriptions, fetchPrescriptions, isLoading } = useMedicineStore();
  const [manualMedicineModal, setManualMedicineModal] = useState<{
    isOpen: boolean;
    prescriptionId?: string;
    extractedMedicines?: string[];
  }>({ isOpen: false });

  // Fetch real prescriptions on component mount
  useEffect(() => {
    fetchPrescriptions();
  }, [fetchPrescriptions]);

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/patient' },
            { label: 'Prescriptions', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Prescriptions</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Upload and manage your prescription documents
              </p>
            </div>
          </div>
        </div>

        {/* Upload Section */}
        <div className="modern-card">
          <PrescriptionUpload />
        </div>

        {/* Prescription History */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <span>Prescription History</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {isLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading prescriptions...</p>
                </div>
              ) : prescriptions.length === 0 ? (
                <div className="text-center py-12">
                  <div className="relative">
                    <FileText className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">No prescriptions uploaded yet</h3>
                  <p className="text-muted-foreground mb-6">Upload your first prescription to get started with AI-powered medication management</p>
                  <Button className="btn-primary">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Prescription
                  </Button>
                </div>
              ) : (
                prescriptions.map((prescription) => (
                  <div key={prescription.id} className="modern-card p-6 hover:scale-[1.01] transition-all duration-300">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                          <FileText className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg text-foreground">{prescription.filename}</h3>
                          <p className="text-sm text-muted-foreground flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            Uploaded {(() => {
                              try {
                                if (!prescription.uploadedAt) return 'Unknown date';
                                const date = prescription.uploadedAt instanceof Date
                                  ? prescription.uploadedAt
                                  : new Date(prescription.uploadedAt);
                                return !isNaN(date.getTime())
                                  ? format(date, 'MMM d, yyyy')
                                  : 'Unknown date';
                              } catch (error) {
                                return 'Unknown date';
                              }
                            })()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge 
                          variant={
                            prescription.status === 'completed' ? 'default' :
                            prescription.status === 'processing' ? 'secondary' :
                            'destructive'
                          }
                          className={`modern-badge ${
                            prescription.status === 'completed' ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10 text-green-700 border-green-200' :
                            prescription.status === 'processing' ? 'bg-gradient-to-r from-blue-500/10 to-indigo-500/10 text-blue-700 border-blue-200' :
                            'bg-gradient-to-r from-red-500/10 to-pink-500/10 text-red-700 border-red-200'
                          }`}
                        >
                          {prescription.status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                          {prescription.status === 'processing' && <Clock className="h-3 w-3 mr-1" />}
                          {prescription.status === 'failed' && <AlertCircle className="h-3 w-3 mr-1" />}
                          {prescription.status}
                        </Badge>
                        <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="hover:bg-blue-50 text-blue-600 border-blue-200 interactive-element"
                          onClick={() => setManualMedicineModal({
                            isOpen: true,
                            prescriptionId: prescription.id,
                            extractedMedicines: []
                          })}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Medicines
                        </Button>
                      </div>
                    </div>
                    
                    {prescription.status === 'completed' && (
                      <>
                        {/* Extracted Text Section */}
                        {prescription.extractedText && (
                          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800 mb-4">
                            <div className="flex items-center space-x-2 mb-3">
                              <FileText className="h-4 w-4 text-blue-600" />
                              <h4 className="font-semibold text-sm text-blue-900 dark:text-blue-100">AWS Textract Extracted Text:</h4>
                              {prescription.confidence && (
                                <Badge variant="secondary" className="text-xs">
                                  {Math.round(prescription.confidence)}% confidence
                                </Badge>
                              )}
                            </div>
                            <div className="bg-white/50 dark:bg-black/20 rounded-lg p-3 border border-blue-100 dark:border-blue-800">
                              <pre className="text-sm text-blue-900 dark:text-blue-100 whitespace-pre-wrap font-mono">
                                {prescription.extractedText}
                              </pre>
                            </div>
                          </div>
                        )}

                        {/* Medicines Section */}
                        {prescription.medicines && prescription.medicines.length > 0 && (
                          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4 border border-green-200 dark:border-green-800">
                            <div className="flex items-center space-x-2 mb-3">
                              <Sparkles className="h-4 w-4 text-green-600" />
                              <h4 className="font-semibold text-sm text-green-900 dark:text-green-100">AI Extracted Medicines:</h4>
                            </div>
                            <div className="space-y-3">
                              {prescription.medicines.map((medicine, index) => (
                                <div key={medicine.id || index} className="flex items-center justify-between p-3 bg-white/50 dark:bg-black/20 rounded-lg border border-green-100 dark:border-green-800">
                                  <div className="flex items-center space-x-3">
                                    <div className="p-1.5 bg-green-100 dark:bg-green-800 rounded-lg">
                                      <FileText className="h-3 w-3 text-green-600 dark:text-green-300" />
                                    </div>
                                    <div>
                                      <span className="font-semibold text-green-900 dark:text-green-100">{medicine.name}</span>
                                      <span className="text-sm text-green-700 dark:text-green-300 ml-2">
                                        {medicine.dosage} - {medicine.frequency}
                                      </span>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                                    <Clock className="h-3 w-3" />
                                    <span>{medicine.duration} days</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Manual Medicine Modal */}
      <ManualMedicineModal
        isOpen={manualMedicineModal.isOpen}
        onClose={() => setManualMedicineModal({ isOpen: false })}
        prescriptionId={manualMedicineModal.prescriptionId}
        extractedMedicineNames={manualMedicineModal.extractedMedicines}
        onMedicinesAdded={() => {
          fetchPrescriptions();
        }}
      />
    </DashboardLayout>
  );
}