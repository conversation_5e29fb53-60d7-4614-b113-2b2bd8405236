'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, X, Pill, Save, AlertCircle } from 'lucide-react';
import { MedicineService } from '@/lib/api/medicines';
import { useMedicineStore } from '@/lib/store';
import { toast } from 'sonner';

interface Medicine {
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  startDate: string;
  endDate: string;
}

interface ManualMedicineModalProps {
  isOpen: boolean;
  onClose: () => void;
  prescriptionId?: string;
  extractedMedicineNames?: string[];
  onMedicinesAdded?: () => void;
}

export function ManualMedicineModal({
  isOpen,
  onClose,
  prescriptionId,
  extractedMedicineNames = [],
  onMedicinesAdded
}: ManualMedicineModalProps) {
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { fetchMedicines } = useMedicineStore();

  // Initialize with extracted medicine names if available
  const initializeFromExtracted = () => {
    const today = new Date().toISOString().split('T')[0];
    const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    const initialMedicines = extractedMedicineNames.map(name => ({
      name: name.replace(/^(TAB\.|TABLET|CAP\.|CAPSULE)\s*/i, '').trim(),
      dosage: 'As prescribed',
      frequency: 'As prescribed',
      duration: '30',
      instructions: 'Take as directed by your doctor',
      startDate: today,
      endDate: endDate,
    }));

    setMedicines(initialMedicines.length > 0 ? initialMedicines : [createEmptyMedicine()]);
  };

  const createEmptyMedicine = (): Medicine => {
    const today = new Date().toISOString().split('T')[0];
    const endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    return {
      name: '',
      dosage: '',
      frequency: 'Once daily',
      duration: '30',
      instructions: 'Take as directed by your doctor',
      startDate: today,
      endDate: endDate,
    };
  };

  const addMedicine = () => {
    setMedicines([...medicines, createEmptyMedicine()]);
  };

  const removeMedicine = (index: number) => {
    setMedicines(medicines.filter((_, i) => i !== index));
  };

  const updateMedicine = (index: number, field: keyof Medicine, value: string) => {
    setMedicines(medicines.map((med, i) => 
      i === index ? { ...med, [field]: value } : med
    ));
  };

  const handleSave = async () => {
    // Validate medicines
    const validMedicines = medicines.filter(med => med.name.trim() && med.dosage.trim());
    
    if (validMedicines.length === 0) {
      toast.error('Please add at least one medicine with name and dosage');
      return;
    }

    setIsLoading(true);
    try {
      // Create medicines
      for (const medicine of validMedicines) {
        await MedicineService.createMedicine({
          name: medicine.name,
          dosage: medicine.dosage,
          frequency: medicine.frequency,
          duration: medicine.duration,
          instructions: medicine.instructions,
          start_date: medicine.startDate,
          end_date: medicine.endDate,
          prescription_id: prescriptionId,
          is_active: true,
        });
      }

      toast.success(`Successfully added ${validMedicines.length} medicine(s)`);
      
      // Refresh medicines in store
      await fetchMedicines();
      
      // Call callback if provided
      if (onMedicinesAdded) {
        onMedicinesAdded();
      }
      
      // Reset and close
      setMedicines([]);
      onClose();
      
    } catch (error: any) {
      console.error('Error creating medicines:', error);
      toast.error(error.message || 'Failed to add medicines');
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize medicines when modal opens
  const handleOpenChange = (open: boolean) => {
    if (open && medicines.length === 0) {
      initializeFromExtracted();
    } else if (!open) {
      setMedicines([]);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Pill className="h-5 w-5 text-blue-600" />
            <span>Add Medicines Manually</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {extractedMedicineNames.length > 0 && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">AI Extracted Medicine Names:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {extractedMedicineNames.map((name, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {name}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="space-y-4">
            {medicines.map((medicine, index) => (
              <Card key={index} className="border-2 border-dashed border-gray-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-gray-900">Medicine {index + 1}</h4>
                    {medicines.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeMedicine(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor={`name-${index}`}>Medicine Name *</Label>
                      <Input
                        id={`name-${index}`}
                        value={medicine.name}
                        onChange={(e) => updateMedicine(index, 'name', e.target.value)}
                        placeholder="e.g., Metformin"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor={`dosage-${index}`}>Dosage *</Label>
                      <Input
                        id={`dosage-${index}`}
                        value={medicine.dosage}
                        onChange={(e) => updateMedicine(index, 'dosage', e.target.value)}
                        placeholder="e.g., 500mg"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor={`frequency-${index}`}>Frequency</Label>
                      <Select
                        value={medicine.frequency}
                        onValueChange={(value) => updateMedicine(index, 'frequency', value)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Once daily">Once daily</SelectItem>
                          <SelectItem value="Twice daily">Twice daily</SelectItem>
                          <SelectItem value="Three times daily">Three times daily</SelectItem>
                          <SelectItem value="Four times daily">Four times daily</SelectItem>
                          <SelectItem value="As needed">As needed</SelectItem>
                          <SelectItem value="As prescribed">As prescribed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor={`duration-${index}`}>Duration (days)</Label>
                      <Input
                        id={`duration-${index}`}
                        type="number"
                        value={medicine.duration}
                        onChange={(e) => updateMedicine(index, 'duration', e.target.value)}
                        placeholder="30"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor={`startDate-${index}`}>Start Date</Label>
                      <Input
                        id={`startDate-${index}`}
                        type="date"
                        value={medicine.startDate}
                        onChange={(e) => updateMedicine(index, 'startDate', e.target.value)}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor={`endDate-${index}`}>End Date</Label>
                      <Input
                        id={`endDate-${index}`}
                        type="date"
                        value={medicine.endDate}
                        onChange={(e) => updateMedicine(index, 'endDate', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <Label htmlFor={`instructions-${index}`}>Instructions</Label>
                    <Textarea
                      id={`instructions-${index}`}
                      value={medicine.instructions}
                      onChange={(e) => updateMedicine(index, 'instructions', e.target.value)}
                      placeholder="Take as directed by your doctor"
                      className="mt-1"
                      rows={2}
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Button
            variant="outline"
            onClick={addMedicine}
            className="w-full border-dashed border-2 border-gray-300 hover:border-blue-400"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Medicine
          </Button>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Medicines
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
